import { Injectable } from '@nestjs/common';
import { RedisService } from '@crednet/utils';
import { SetOptions } from 'redis';

@Injectable()
export class InternalCacheService {
  private _tags: string[] = [];

  constructor(private readonly redisService: RedisService) {}

  /**
   * Get data from cache
   * @param {...[string]} tags
   * @returns
   */
  tags(...tags: string[]) {
    this._tags = [...this._tags, ...tags.flat()];
    return this;
  }

  /**
   * Get data from cache
   * @param {string} key
   * @returns
   */
  async get<T>(key: string): Promise<T> {
    const data = await this.redisService.get(key);
    let decoded: T;

    try {
      decoded = JSON.parse(data);
    } catch {
      /* empty */
    }

    return decoded || (data as T);
  }

  /**
   * save data to cache
   *
   * @async
   * @template T
   * @param {string} key - unique key used in saving data to cache
   * @param {T} value
   * @param {?number} [ttl] - time in seconds to persist data in cache
   * @returns {*}
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    // Format data to string.
    const stringValue = JSON.stringify(value);

    // Create sets for each tag.
    for (const tag of this._tags) {
      await this.redisService.getClient().sAdd(`crypto:tag:${tag}`, key);
    }

    // Save data to cache.
    const opts: SetOptions = ttl ? { EX: ttl } : {};
    await this.redisService.set(key, stringValue, opts);
  }

  /**
   * remove data from cache using key
   *
   * @async
   * @param {string} key
   * @returns {Promise<void>}
   */
  async delete(key: string): Promise<void> {
    // Delete keys from all tag sets.
    for (const tag of this._tags) {
      await this.redisService.getClient().sRem(`crypto:tag:${tag}`, key);
    }

    await this.redisService.del(key);
  }

  /**
   * remove data from cache using key
   *
   * @async
   * @returns {Promise<void>}
   */
  async flush(): Promise<void> {
    // Delete all keys if tags weren't specified.
    if (this._tags.length < 1) {
      throw new Error('Flushing database not allowed.');
    }

    // Get and transform tags
    const tags = this._tags.map((tag) => `crypto:tag:${tag}`);

    // Get all keys in all tag sets.
    const keys = await this.redisService.getClient().sUnion(tags);

    // Delete all keys and tags.
    await this.redisService.getClient().del([...tags, ...keys]);
  }

  /**
   * returns and caches data passed in
   *
   * @async
   * @template T
   * @param {string} key
   * @param {T | function(): Promise<T>} value
   * @param {?number} [ttl]
   * @returns {Promise<T>}
   */
  async remember<T>(
    key: string,
    value: T | (() => Promise<T>),
    ttl?: number,
  ): Promise<T> {
    const data = await this.get<T>(key);
    if (data) {
      return data;
    }
    const newData =
      typeof value === 'function' ? await (value as () => Promise<T>)() : value;
    await this.set(key, newData, ttl);
    return newData;
  }

  /**
   * Adds a key to a Redis Set to track stored entities of a type.
   * Example: `addToCollection('crypto:currencies', 'crypto:currency_USD')`
   */
  async addToCollection(collection: string, key: string): Promise<void> {
    await this.redisService.getClient().sAdd(collection, key);
  }

  /**
   * Retrieves all items of a given collection (e.g., 'crypto:currencies', 'crypto:users')
   */
  async getAllFromCollection<T>(collection: string): Promise<T[]> {
    const keys = await this.redisService.getClient().sMembers(collection);

    if (!keys.length) return [];

    // Get all values using mGet
    const values = await Promise.all(
      keys.map(async (key) => {
        const data = await this.get<T>(key);
        return data;
      }),
    );

    return values.filter(Boolean);
  }

  /**
   * Removes a key from a collection when deleting/updating.
   */
  async removeFromCollection(collection: string, key: string): Promise<void> {
    await this.redisService.getClient().sRem(collection, key);
  }

  async keys(pattern?: string): Promise<string[]> {
    return this.redisService.getClient().keys(pattern || '*');
  }

  async hgetall(key: string): Promise<Record<string, string>> {
    return this.redisService.findOne('', key);
  }
}
