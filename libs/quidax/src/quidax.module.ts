import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import config from 'src/config';
import { QuidaxService } from './quidax.service';

@Module({
  imports: [
    HttpModule.register({
      timeout: 100000,
      maxRedirects: 5,
      baseURL: config.quidax.baseUrl,
      headers: {
        Authorization: `Bearer ${config.quidax.apiKey}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    }),
  ],
  providers: [QuidaxService],
  exports: [QuidaxService],
})
export class QuidaxModule {}
