{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true}, "projects": {"qudiax": {"type": "library", "root": "libs/qudiax", "entryFile": "index", "sourceRoot": "libs/qudiax/src", "compilerOptions": {"tsConfigPath": "libs/qudiax/tsconfig.lib.json"}}, "rabbitmq": {"type": "library", "root": "libs/rabbitmq", "entryFile": "index", "sourceRoot": "libs/rabbitmq/src", "compilerOptions": {"tsConfigPath": "libs/rabbitmq/tsconfig.lib.json"}}}}