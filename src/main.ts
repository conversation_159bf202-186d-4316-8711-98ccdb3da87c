import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';
// import { Transport } from '@nestjs/microservices';
import config from './config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import {
  CustomValidationPipe,
  ExceptionsFilter,
  ResponseInterceptor,
} from '@crednet/utils';

const port = process.env.PORT;

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {});

  app.setGlobalPrefix('api', { exclude: ['docs', 'health'] });
  app.useGlobalFilters(new ExceptionsFilter());
  app.useGlobalPipes(new CustomValidationPipe());
  app.useGlobalInterceptors(new ResponseInterceptor());

  if (config.env != 'production') {
    const swaggerConfig = new DocumentBuilder()
      .setTitle('Crypto')
      .setDescription('Crypto Service')
      .setVersion('v1')
      .addServer(config.baseUrl)
      .addBearerAuth(
        {
          description: `Please enter token in following format: Bearer <JWT>`,
          name: 'Authorization',
          bearerFormat: 'Bearer',
          scheme: 'Bearer',
          type: 'http',
          in: 'Header',
        },
        'JWT',
      )
      .build();
    const document = SwaggerModule.createDocument(app, swaggerConfig);
    SwaggerModule.setup('docs', app, document);
  }

  app.enableCors({
    origin: [config.baseUrl, 'http://localhost:3000'],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    preflightContinue: false,
    credentials: true,
  });

  await app.listen(port);
}

bootstrap().then(() => {
  Logger.log(`
      ------------
      Server Application Started!
      API V1: ${config.baseUrl}
      API Docs: ${config.baseUrl}/docs
      Microserservice Started Successfully
      ------------
`);
});
