import * as path from 'node:path';
import * as dotenv from 'dotenv';

dotenv.config({
  path: path.resolve(process.cwd(), './.env'),
});

export default {
  port: Number.parseInt(process.env.PORT, 10),
  env: process.env.ENV,
  nodeEnv: process.env.NODE_ENV,
  baseUrl: process.env.BASE_URL || `http://127.0.0.1:${process.env.PORT}`,
  jwt: {
    publicKey: process.env.RSA_PUBLIC_KEY,
    issuer: process.env.ISSUER || 'crednet/auth', // time in seconds
  },
  db: {
    url: process.env.DATABASE_URL,
  },
  rabbitMq: {
    brockers: process.env.RABBIT_MQ_BROCKERS?.split(','),
  },
  redis: {
    host: process.env.REDIS_HOST,
    url: process.env.REDIS_URL,
    user: process.env.REDIS_USER,
    password: process.env.REDIS_PASS,
    port: Number.parseInt(process.env.REDIS_PORT, 10),
  },
  quidax: {
    baseUrl: process.env.QUIDAX_BASE_URL,
    apiKey: process.env.QUIDAX_API_KEY,
  },
  webhookSecret: process.env.WEBHOOK_SECRET,
};
