import { Injectable } from '@nestjs/common';
import { RabbitmqService } from '@crednet/utils';
import { Events } from '../../utils/queue';
import { BaseWebhookConsumer } from '../../utils/webhook/base-webhook.consumer';
import { WebhookEventHandlerFactory } from '../../utils/webhook/webhook-event.handler';
import {
  OrderDoneEventHandler,
  OrderCancelledEventHandler,
} from './orders.event-handlers';

@Injectable()
export class OrdersWebhookConsumer extends BaseWebhookConsumer {
  constructor(
    protected readonly rmqService: RabbitmqService,
    private readonly eventHandlerFactory: WebhookEventHandlerFactory,
    private readonly orderDoneHandler: OrderDoneEventHandler,
    private readonly orderCancelledHandler: OrderCancelledEventHandler,
  ) {
    super(rmqService);
  }

  /**
   * Register event handlers for order events
   */
  protected registerEventHandlers(): void {
    // Register event handlers with the factory
    this.eventHandlerFactory.registerHandler(
      Events.ORDER_DONE,
      this.orderDoneHandler,
    );
    this.eventHandlerFactory.registerHandler(
      Events.ORDER_CANCELLED,
      this.orderCancelledHandler,
    );

    // Subscribe to events
    this.subscribe(Events.ORDER_DONE);
    this.subscribe(Events.ORDER_CANCELLED);
  }

  /**
   * Get the handler for an event
   * @param eventName The event name
   * @returns A function that handles the event
   */
  protected getEventHandler(eventName: string): (data: any) => Promise<void> {
    const handler = this.eventHandlerFactory.getHandler(eventName);
    if (handler) {
      return handler.handle.bind(handler);
    }
    return null;
  }
}
