import { BadRequestException, Injectable } from '@nestjs/common';
import { DepositRepository } from '../repositories/deposits.repository';
import {
  Deposit,
  DepositStatus,
  PaymentStatus,
} from '../entities/deposits.entity';
import { UserRepository } from '../repositories/users.repository';
import { WalletRepository } from '../repositories/wallet.repository';
import { GetDepositsQueryDto } from '../dtos/deposit.dto';
import { QuidaxService } from '@app/quidax';

@Injectable()
export class DepositsService {
  constructor(
    private readonly depositRepository: DepositRepository,
    private readonly userRepository: UserRepository,
    private readonly walletRepository: WalletRepository,
    private readonly quidaxService: QuidaxService,
  ) {}

  async createDeposit(depositData: any): Promise<Deposit> {
    const {
      type,
      currency,
      amount,
      fee,
      txid,
      status,
      reason,
      wallet,
      user,
      payment_transaction,
      payment_address,
    } = depositData;

    const walletId = wallet?.id;
    const userId = user?.id;

    const getUser = await this.userRepository.findOne({
      where: {
        id: userId,
      },
    });

    if (!getUser) {
      console.error('User not found');
    }

    const getWallet = await this.walletRepository.findOne({
      where: {
        id: walletId,
      },
    });

    if (!getWallet) {
      const fetchWallet = await this.quidaxService.fetchWallet(
        user.id,
        currency,
      );

      await this.walletRepository.createWallet(
        {
          id: fetchWallet.id,
          currency: fetchWallet.currency,
          balance: fetchWallet.balance,
          depositAddress: fetchWallet.deposit_address,
          convertedBalance: fetchWallet.converted_balance,
          locked: fetchWallet.locked,
          staked: fetchWallet.staked,
          destinationTag: fetchWallet.destination_tag,
        },
        user.id,
      );
    }

    // Create a new deposit entity
    const deposit = new Deposit();
    deposit.id = depositData.id;
    deposit.type = type;
    deposit.currency = currency;
    deposit.amount = amount;
    deposit.fee = fee;
    deposit.txid = txid || null;
    deposit.status = status as DepositStatus;
    deposit.reason = reason || null;
    deposit.wallet = getWallet;
    deposit.user = getUser;
    deposit.paymentStatus =
      (payment_transaction?.status as PaymentStatus) ||
      PaymentStatus.UNCONFIRMED;
    deposit.confirmations = payment_transaction?.confirmations || null;
    deposit.requiredConfirmations =
      payment_transaction?.required_confirmations || null;
    deposit.paymentAddress = payment_address?.address || null;
    deposit.paymentReference = payment_address?.reference || null;
    deposit.paymentNetwork = payment_address?.network || null;
    deposit.sender = depositData.sender || null;

    return this.depositRepository.save(deposit);
  }

  async getDeposit(depositId: string): Promise<Deposit> {
    const deposit = await this.depositRepository.findOne({
      where: {
        id: depositId,
      },
      relations: ['user', 'wallet'],
    });

    if (!deposit) {
      throw new BadRequestException(`Deposit with ID ${depositId} not found`);
    }

    return deposit;
  }

  async getAllWalletDeposits(
    walletId: string,
    getDepositsQueryDto: GetDepositsQueryDto,
  ): Promise<Deposit[]> {
    return this.depositRepository.getDepositsByWallet(
      walletId,
      new Date(getDepositsQueryDto.startDate),
      new Date(getDepositsQueryDto.endDate),
      getDepositsQueryDto.page,
      getDepositsQueryDto.limit,
      getDepositsQueryDto.status,
    );
  }

  async getAllUserDeposits(
    userId: string,
    getDepositsQueryDto: GetDepositsQueryDto,
  ): Promise<Deposit[]> {
    return this.depositRepository.getDepositsByWallet(
      userId,
      new Date(getDepositsQueryDto.startDate),
      new Date(getDepositsQueryDto.endDate),
      getDepositsQueryDto.page,
      getDepositsQueryDto.limit,
      getDepositsQueryDto.status,
    );
  }
}
