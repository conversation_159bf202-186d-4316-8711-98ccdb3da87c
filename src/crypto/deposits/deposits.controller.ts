import { Controller, UseGuards, Get, Param, Query } from '@nestjs/common';
import { DepositsService } from './deposits.service';
import { JwtAuthGuard } from '@crednet/authmanager';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { GetDepositsQueryDto } from '../dtos/deposit.dto';
import { Deposit } from '../entities/deposits.entity';

@Controller('deposits')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('deposits')
export class DepositsController {
  constructor(private readonly depositsService: DepositsService) {}

  @Get('wallet/:walletId')
  async getAllWalletDeposits(
    @Param('walletId') walletId: string,
    @Query() getDepositsQueryDto: GetDepositsQueryDto,
  ): Promise<Deposit[]> {
    return this.depositsService.getAllWalletDeposits(
      walletId,
      getDepositsQueryDto,
    );
  }

  @Get('user/:userId')
  async getAllUserDeposits(
    @Param('userId') userId: string,
    @Query() getDepositsQueryDto: GetDepositsQueryDto,
  ): Promise<Deposit[]> {
    return this.depositsService.getAllUserDeposits(userId, getDepositsQueryDto);
  }

  @Get(':id')
  async getDeposit(@Param('id') id: string): Promise<Deposit> {
    return this.depositsService.getDeposit(id);
  }
}
