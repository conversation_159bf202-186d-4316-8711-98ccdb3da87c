import { Injectable } from '@nestjs/common';
import { RabbitmqService } from '@crednet/utils';
import { Events } from '../../utils/queue';
import { BaseWebhookConsumer } from '../../utils/webhook/base-webhook.consumer';
import { WebhookEventHandlerFactory } from '../../utils/webhook/webhook-event.handler';
import {
  DepositCompletedEventHandler,
  DepositFailedAmlEventHandler,
  DepositOnHoldEventHandler,
  DepositSuccessfulEventHandler,
} from './deposits.event-handlers';

@Injectable()
export class DepositsWebhookConsumer extends BaseWebhookConsumer {
  constructor(
    protected readonly rmqService: RabbitmqService,
    private readonly eventHandlerFactory: WebhookEventHandlerFactory,
    private readonly depositCompletedHandler: DepositCompletedEventHandler,
    private readonly depositFailedAmlHandler: DepositFailedAmlEventHandler,
    private readonly depositOnHoldHandler: DepositOnHoldEventHandler,
    private readonly depositSuccessfulHandler: DepositSuccessfulEventHandler,
  ) {
    super(rmqService);
  }

  /**
   * Register event handlers for deposit events
   */
  protected registerEventHandlers(): void {
    // Register event handlers with the factory
    this.eventHandlerFactory.registerHandler(
      Events.DEPOSIT_COMPLETED,
      this.depositCompletedHandler,
    );
    this.eventHandlerFactory.registerHandler(
      Events.DEPOSIT_FAILED_AML,
      this.depositFailedAmlHandler,
    );
    this.eventHandlerFactory.registerHandler(
      Events.DEPOSIT_ON_HOLD,
      this.depositOnHoldHandler,
    );
    this.eventHandlerFactory.registerHandler(
      Events.DEPOSIT_SUCCESSFUL,
      this.depositSuccessfulHandler,
    );

    // Subscribe to events
    this.subscribe(Events.DEPOSIT_COMPLETED);
    this.subscribe(Events.DEPOSIT_FAILED_AML);
    this.subscribe(Events.DEPOSIT_ON_HOLD);
    this.subscribe(Events.DEPOSIT_SUCCESSFUL);
  }

  /**
   * Get the handler for an event
   * @param eventName The event name
   * @returns A function that handles the event
   */
  protected getEventHandler(eventName: string): (data: any) => Promise<void> {
    const handler = this.eventHandlerFactory.getHandler(eventName);
    if (handler) {
      return handler.handle.bind(handler);
    }
    return null;
  }
}
