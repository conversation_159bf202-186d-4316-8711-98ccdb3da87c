import {
  Controller,
  Get,
  Query,
  Param,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { GetAuthData, JwtAuthGuard, AuthData } from '@crednet/authmanager';
import { TransactionHistoryService } from '../services/transaction-history.service';
import {
  TransactionHistoryFilterDto,
  PaginationDto,
  TransactionHistoryDto,
  PaginatedTransactionHistoryDto,
} from '../dtos/transaction-history.dto';
import {
  TransactionHistoryType,
  TransactionHistoryStatus,
} from '../entities/transaction-history.entity';

@Controller('transaction-history')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('transaction-history')
export class TransactionHistoryController {
  constructor(
    private readonly transactionHistoryService: TransactionHistoryService,
  ) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get transaction history for authenticated user',
    description: 'Retrieve paginated transaction history with optional filters',
  })
  @ApiQuery({
    name: 'type',
    enum: TransactionHistoryType,
    required: false,
    description: 'Filter by transaction type',
  })
  @ApiQuery({
    name: 'status',
    enum: TransactionHistoryStatus,
    required: false,
    description: 'Filter by transaction status',
  })
  @ApiQuery({
    name: 'currency',
    type: String,
    required: false,
    description: 'Filter by currency code',
  })
  @ApiQuery({
    name: 'startDate',
    type: String,
    required: false,
    description: 'Filter by start date (ISO string)',
  })
  @ApiQuery({
    name: 'endDate',
    type: String,
    required: false,
    description: 'Filter by end date (ISO string)',
  })
  @ApiQuery({
    name: 'sourceType',
    type: String,
    required: false,
    description: 'Filter by source type (order, swap, transaction, deposit)',
  })
  @ApiQuery({
    name: 'sourceReference',
    type: String,
    required: false,
    description: 'Filter by source reference',
  })
  @ApiQuery({
    name: 'page',
    type: Number,
    required: false,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    type: Number,
    required: false,
    description: 'Items per page (default: 10, max: 100)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Transaction history retrieved successfully',
    type: PaginatedTransactionHistoryDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Bad request - Invalid query parameters',
  })
  async getTransactionHistory(
    @GetAuthData() auth: AuthData,
    @Query() filters: TransactionHistoryFilterDto,
    @Query() pagination: PaginationDto,
  ): Promise<PaginatedTransactionHistoryDto> {
    return this.transactionHistoryService.getTransactionHistory(
      auth,
      filters,
      pagination,
    );
  }

  @Get('/wallet/:walletId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get transaction history for a specific wallet',
    description: 'Retrieve paginated transaction history for a wallet with optional filters',
  })
  @ApiParam({
    name: 'walletId',
    type: String,
    description: 'Wallet ID to get transaction history for',
  })
  @ApiQuery({
    name: 'type',
    enum: TransactionHistoryType,
    required: false,
    description: 'Filter by transaction type',
  })
  @ApiQuery({
    name: 'status',
    enum: TransactionHistoryStatus,
    required: false,
    description: 'Filter by transaction status',
  })
  @ApiQuery({
    name: 'currency',
    type: String,
    required: false,
    description: 'Filter by currency code',
  })
  @ApiQuery({
    name: 'startDate',
    type: String,
    required: false,
    description: 'Filter by start date (ISO string)',
  })
  @ApiQuery({
    name: 'endDate',
    type: String,
    required: false,
    description: 'Filter by end date (ISO string)',
  })
  @ApiQuery({
    name: 'sourceType',
    type: String,
    required: false,
    description: 'Filter by source type (order, swap, transaction, deposit)',
  })
  @ApiQuery({
    name: 'sourceReference',
    type: String,
    required: false,
    description: 'Filter by source reference',
  })
  @ApiQuery({
    name: 'page',
    type: Number,
    required: false,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    type: Number,
    required: false,
    description: 'Items per page (default: 10, max: 100)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Wallet transaction history retrieved successfully',
    type: PaginatedTransactionHistoryDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Wallet not found or access denied',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Bad request - Invalid parameters',
  })
  async getWalletTransactionHistory(
    @GetAuthData() auth: AuthData,
    @Param('walletId') walletId: string,
    @Query() filters: TransactionHistoryFilterDto,
    @Query() pagination: PaginationDto,
  ): Promise<PaginatedTransactionHistoryDto> {
    return this.transactionHistoryService.getWalletTransactionHistory(
      auth,
      walletId,
      filters,
      pagination,
    );
  }

  @Get('/:reference')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get transaction history by reference',
    description: 'Retrieve a specific transaction history record by its reference',
  })
  @ApiParam({
    name: 'reference',
    type: String,
    description: 'Transaction history reference',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Transaction history retrieved successfully',
    type: TransactionHistoryDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Transaction history not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Access denied to this transaction history',
  })
  async getTransactionHistoryByReference(
    @GetAuthData() auth: AuthData,
    @Param('reference') reference: string,
  ): Promise<TransactionHistoryDto> {
    return this.transactionHistoryService.getTransactionHistoryByReference(
      auth,
      reference,
    );
  }
}
