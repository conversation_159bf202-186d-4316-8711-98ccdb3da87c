import { Modu<PERSON> } from '@nestjs/common';
import { TransactionHistoryController } from './transaction-history.controller';
import { TransactionHistoryService } from '../services/transaction-history.service';
import { ErrorHandlingService } from '../services/error-handling.service';
import { TransactionHistoryRepository } from '../repositories/transaction-history.repository';
import { WalletRepository } from '../repositories/wallet.repository';
import { UserRepository } from '../repositories/users.repository';
import { InternalCacheModule } from '@app/internal-cache';

@Module({
  imports: [InternalCacheModule],
  controllers: [TransactionHistoryController],
  providers: [
    TransactionHistoryService,
    ErrorHandlingService,
    TransactionHistoryRepository,
    WalletRepository,
    UserRepository,
  ],
  exports: [
    TransactionHistoryService,
    ErrorHandlingService,
    TransactionHistoryRepository,
  ],
})
export class TransactionHistoryModule {}
