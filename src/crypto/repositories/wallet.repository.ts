import { BadRequestException, Injectable } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { Wallet } from '../entities/wallet.entity';
import { DataSource } from 'typeorm';
import { CreateWalletDto } from '../dtos/wallet.dto';
import { UserRepository } from './users.repository';
import { CurrencyRepository } from './currency.repository';

@Injectable()
export class WalletRepository extends TypeOrmRepository<Wallet> {
  constructor(
    private readonly dataSource: DataSource,
    private readonly userRepository: UserRepository,
    private readonly currencyRepository: CurrencyRepository,
  ) {
    super(Wallet, dataSource.createEntityManager());
  }

  async getUserWallet(walletId: string): Promise<Wallet> {
    return await this.findOne({
      where: { id: walletId },
      relations: ['user', 'currency', 'currency.networks'],
    });
  }

  async createWallet(
    createWalletDto: CreateWalletDto,
    userId: string,
  ): Promise<Wallet> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });
    if (!user) throw new BadRequestException('User not found');

    const currency = await this.currencyRepository.findOne({
      where: { currencyCode: createWalletDto.currency },
    });
    if (!currency) throw new BadRequestException('Currency not found');

    const wallet = await this.save({
      ...createWalletDto,
      user,
      currency,
    });

    return wallet;
  }

  async createWalletDefault(
    createWalletDto: CreateWalletDto,
    userId: string,
  ): Promise<Wallet> {
    const user = await this.userRepository.findOne({
      where: { userId: userId },
    });

    if (!user) throw new BadRequestException('User not found');

    const currency = await this.currencyRepository.findOne({
      where: { currencyCode: createWalletDto.currency },
    });
    if (!currency) throw new BadRequestException('Currency not found');

    const wallet = await this.save({
      ...createWalletDto,
      user,
      currency,
    });

    return wallet;
  }

  async getUserWalletByCurrency(
    userId: string,
    currencyCode: string,
  ): Promise<Wallet | undefined> {
    return await this.findOne({
      where: {
        user: { id: userId },
        currency: { currencyCode },
      },
      relations: ['user', 'currency', 'currency.networks'],
    });
  }

  async getUserWalletByAuthIdAndCurrency(
    userId: string,
    currencyCode: string,
  ): Promise<Wallet | undefined> {
    return await this.findOne({
      where: {
        user: { userId: userId },
        currency: { currencyCode },
      },
      relations: ['user', 'currency', 'currency.networks'],
    });
  }

  async getAllUserActiveWallets(userId: string): Promise<Wallet[]> {
    return await this.find({
      where: {
        user: { userId: userId },
      },
      relations: ['user', 'currency', 'currency.networks'],
    });
  }

  async getUserWallets(userId: string): Promise<Wallet[]> {
    return await this.find({
      where: {
        user: { id: userId },
      },
      relations: ['user', 'currency', 'currency.networks'],
    });
  }

  async updateWallet(
    walletId: string,
    updateData: Partial<Wallet>,
  ): Promise<Wallet> {
    await this.update({ id: walletId }, updateData);
    return this.getUserWallet(walletId);
  }
}
