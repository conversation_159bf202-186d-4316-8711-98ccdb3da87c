import { Injectable, NotFoundException } from '@nestjs/common';
import { Orders } from '../entities/orders.entity';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { UserRepository } from './users.repository';
import { OrderDto } from '../dtos/orders.dto';

@Injectable()
export class OrderRepository extends TypeOrmRepository<Orders> {
  constructor(
    private readonly dataSource: DataSource,
    private readonly userRepository: UserRepository,
  ) {
    super(Orders, dataSource.createEntityManager());
  }

  async createOrder(order: OrderDto): Promise<Orders> {
    const user = await this.userRepository.findOne({
      where: {
        userId: order.userId,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const savedOrder = await this.save({
      ...order,
      user,
    });

    return savedOrder;
  }

  async getOrder(orderId: string): Promise<Orders> {
    const order = await this.findOne({
      where: {
        id: orderId,
      },
      relations: ['user', 'trades'],
    });

    if (!order) {
      throw new NotFoundException(`Order with ID ${orderId} not found`);
    }

    return order;
  }

  async getOrdersByUserId(
    userId: string,
    startDate: Date,
    endDate: Date,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ orders: Orders[]; total: number }> {
    const query = this.createQueryBuilder('orders')
      .where('orders.userId = :userId', { userId })
      .andWhere('orders.createdAt >= :startDate', { startDate })
      .andWhere('orders.createdAt <= :endDate', { endDate })
      .orderBy('orders.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .leftJoinAndSelect('orders.user', 'user');

    const [orders, total] = await query.getManyAndCount();

    return { orders, total };
  }
}
