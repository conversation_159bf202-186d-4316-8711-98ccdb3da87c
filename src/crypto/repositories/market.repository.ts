import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { MarketDataDto } from '../dtos/market.dto';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { Market } from '../entities/markets.entity';

@Injectable()
export class MarketRepository extends TypeOrmRepository<Market> {
  constructor(private readonly dataSource: DataSource) {
    super(Market, dataSource.createEntityManager());
  }

  async createOrUpdateMarkets(marketData: MarketDataDto[]) {
    marketData.forEach(async (market) => {
      const existingMarket = await this.findOne({
        where: { id: market.id },
      });
      if (existingMarket) {
        await this.update({ id: market.id }, market);
      } else {
        await this.save(market);
      }
    });
  }

  /**
   * Get markets with base currency name and image, optionally filter by base currency name
   * @param baseCurrencyName Optional search string for base currency name
   */
  async getMarkets(
    baseCurrencyName?: string,
  ): Promise<
    Array<Market & { baseCurrencyName: string; baseCurrencyImage: string }>
  > {
    const query = this.createQueryBuilder('market')
      .leftJoin(
        'currencies',
        'currency',
        'market.baseUnit = currency.currencyCode',
      )
      .addSelect([
        'currency.name AS baseCurrencyName',
        'currency.image AS baseCurrencyImage',
      ]);

    if (baseCurrencyName) {
      query.andWhere('LOWER(currency.name) LIKE :baseCurrencyName', {
        baseCurrencyName: `%${baseCurrencyName.toLowerCase()}%`,
      });
    }

    query.select(['market', 'currency.name', 'currency.image']);

    const results = await query.getRawAndEntities();

    return results.entities.map((market, idx) => ({
      baseCurrencyName: results.raw[idx].currency_name,
      baseCurrencyImage: results.raw[idx].currency_image,
      ...market,
    }));
  }

  async getMarketById(id: string): Promise<Market> {
    return await this.findOne({
      where: { id },
    });
  }

  async getMarketsByQuotes(): Promise<Record<string, string[]>> {
    const markets = await this.find({
      select: ['id', 'baseUnit', 'quoteUnit'],
    });

    // Group baseUnits by quoteUnit
    const groupedMarkets: Record<string, string[]> = {};

    markets.forEach(({ baseUnit, quoteUnit }) => {
      if (!groupedMarkets[baseUnit]) {
        groupedMarkets[baseUnit] = [];
      }
      if (!groupedMarkets[baseUnit].includes(quoteUnit)) {
        groupedMarkets[baseUnit].push(quoteUnit);
      }
    });

    return groupedMarkets;
  }

  async getMarketsRelativeToUsdt(): Promise<Market[]> {
    return await this.find({
      where: { quoteUnit: 'USDT' },
    });
  }
}
