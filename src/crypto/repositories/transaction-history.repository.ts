import { Injectable, NotFoundException } from '@nestjs/common';
import { DataSource, FindManyOptions, Between, MoreThanOrEqual, LessThanOrEqual } from 'typeorm';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { TransactionHistory } from '../entities/transaction-history.entity';
import { UserRepository } from './users.repository';
import { WalletRepository } from './wallet.repository';
import {
  CreateTransactionHistoryDto,
  TransactionHistoryFilterDto,
  PaginationDto,
} from '../dtos/transaction-history.dto';
import { Pagination } from 'nestjs-typeorm-paginate';

@Injectable()
export class TransactionHistoryRepository extends TypeOrmRepository<TransactionHistory> {
  constructor(
    private readonly dataSource: DataSource,
    private readonly userRepository: UserRepository,
    private readonly walletRepository: WalletRepository,
  ) {
    super(TransactionHistory, dataSource.createEntityManager());
  }

  async createTransactionHistory(
    transactionHistoryDto: CreateTransactionHistoryDto,
  ): Promise<TransactionHistory> {
    const user = await this.userRepository.findOne({
      where: {
        userId: transactionHistoryDto.userId,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const wallet = await this.walletRepository.findOne({
      where: {
        id: transactionHistoryDto.walletId,
        user: {
          id: user.id,
        },
      },
      relations: ['user', 'currency'],
    });

    if (!wallet) {
      throw new NotFoundException(
        'Wallet not found or does not belong to the user',
      );
    }

    const savedTransactionHistory = await this.save({
      ...transactionHistoryDto,
      user,
      wallet,
    });

    return savedTransactionHistory;
  }

  async getTransactionHistoryByUser(
    userId: string,
    filters: TransactionHistoryFilterDto,
    pagination: PaginationDto,
  ): Promise<Pagination<TransactionHistory>> {
    const user = await this.userRepository.findOne({
      where: { userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const queryBuilder = this.createQueryBuilder('transactionHistory')
      .leftJoinAndSelect('transactionHistory.wallet', 'wallet')
      .leftJoinAndSelect('wallet.currency', 'currency')
      .leftJoinAndSelect('transactionHistory.user', 'user')
      .where('transactionHistory.userId = :userId', { userId: user.id });

    // Apply filters
    if (filters.type) {
      queryBuilder.andWhere('transactionHistory.type = :type', {
        type: filters.type,
      });
    }

    if (filters.status) {
      queryBuilder.andWhere('transactionHistory.status = :status', {
        status: filters.status,
      });
    }

    if (filters.currency) {
      queryBuilder.andWhere('transactionHistory.currency = :currency', {
        currency: filters.currency,
      });
    }

    if (filters.sourceType) {
      queryBuilder.andWhere('transactionHistory.sourceType = :sourceType', {
        sourceType: filters.sourceType,
      });
    }

    if (filters.sourceReference) {
      queryBuilder.andWhere(
        'transactionHistory.sourceReference = :sourceReference',
        {
          sourceReference: filters.sourceReference,
        },
      );
    }

    if (filters.startDate && filters.endDate) {
      queryBuilder.andWhere(
        'transactionHistory.createdAt BETWEEN :startDate AND :endDate',
        {
          startDate: new Date(filters.startDate),
          endDate: new Date(filters.endDate),
        },
      );
    } else if (filters.startDate) {
      queryBuilder.andWhere('transactionHistory.createdAt >= :startDate', {
        startDate: new Date(filters.startDate),
      });
    } else if (filters.endDate) {
      queryBuilder.andWhere('transactionHistory.createdAt <= :endDate', {
        endDate: new Date(filters.endDate),
      });
    }

    queryBuilder.orderBy('transactionHistory.createdAt', 'DESC');

    return this.findMany(
      {
        page: pagination.page,
        limit: pagination.limit,
      },
      {
        where: queryBuilder.getQueryAndParameters()[0],
        relations: ['wallet', 'wallet.currency', 'user'],
        order: { createdAt: 'DESC' },
      },
    );
  }

  async getTransactionHistoryByWallet(
    userId: string,
    walletId: string,
    filters: TransactionHistoryFilterDto,
    pagination: PaginationDto,
  ): Promise<Pagination<TransactionHistory>> {
    const user = await this.userRepository.findOne({
      where: { userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const wallet = await this.walletRepository.findOne({
      where: {
        id: walletId,
        user: {
          id: user.id,
        },
      },
    });

    if (!wallet) {
      throw new NotFoundException(
        'Wallet not found or does not belong to the user',
      );
    }

    const whereConditions: any = {
      wallet: { id: walletId },
      user: { id: user.id },
    };

    // Apply filters
    if (filters.type) {
      whereConditions.type = filters.type;
    }

    if (filters.status) {
      whereConditions.status = filters.status;
    }

    if (filters.currency) {
      whereConditions.currency = filters.currency;
    }

    if (filters.sourceType) {
      whereConditions.sourceType = filters.sourceType;
    }

    if (filters.sourceReference) {
      whereConditions.sourceReference = filters.sourceReference;
    }

    if (filters.startDate && filters.endDate) {
      whereConditions.createdAt = Between(
        new Date(filters.startDate),
        new Date(filters.endDate),
      );
    } else if (filters.startDate) {
      whereConditions.createdAt = MoreThanOrEqual(new Date(filters.startDate));
    } else if (filters.endDate) {
      whereConditions.createdAt = LessThanOrEqual(new Date(filters.endDate));
    }

    return this.findMany(
      {
        page: pagination.page,
        limit: pagination.limit,
      },
      {
        where: whereConditions,
        relations: ['wallet', 'wallet.currency', 'user'],
        order: { createdAt: 'DESC' },
      },
    );
  }

  async getTransactionHistoryByReference(
    reference: string,
  ): Promise<TransactionHistory> {
    const transactionHistory = await this.findOne({
      where: { reference },
      relations: ['wallet', 'wallet.currency', 'user'],
    });

    if (!transactionHistory) {
      throw new NotFoundException(
        `Transaction history with reference ${reference} not found`,
      );
    }

    return transactionHistory;
  }

  async getTransactionHistoryBySourceReference(
    sourceReference: string,
  ): Promise<TransactionHistory[]> {
    return this.find({
      where: { sourceReference },
      relations: ['wallet', 'wallet.currency', 'user'],
      order: { createdAt: 'DESC' },
    });
  }

  async updateTransactionHistoryStatus(
    reference: string,
    status: any,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const updateData: any = { status };
    if (metadata) {
      updateData.metadata = metadata;
    }

    await this.update({ reference }, updateData);
  }
}
