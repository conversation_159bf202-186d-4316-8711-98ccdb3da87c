import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { Address } from '../entities/address.entity';
import { DataSource } from 'typeorm';

@Injectable()
export class AddressRepository extends TypeOrmRepository<Address> {
  constructor(private readonly dataSource: DataSource) {
    super(Address, dataSource.createEntityManager());
  }
}
