import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { Trade } from '../entities/trades.entity';
import { DataSource } from 'typeorm';

@Injectable()
export class TradeRepository extends TypeOrmRepository<Trade> {
  constructor(private readonly dataSource: DataSource) {
    super(Trade, dataSource.createEntityManager());
  }
}
