import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { Deposit, DepositStatus } from '../entities/deposits.entity';
import { DataSource } from 'typeorm';

@Injectable()
export class DepositRepository extends TypeOrmRepository<Deposit> {
  constructor(private readonly dataSource: DataSource) {
    super(Deposit, dataSource.createEntityManager());
  }

  async getDepositsByUser(
    userId: string,
    startDate?: Date,
    endDate?: Date,
    page: number = 1,
    limit: number = 10,
    status?: DepositStatus, // Optional status parameter
  ): Promise<Deposit[]> {
    const query = this.dataSource
      .getRepository(Deposit)
      .createQueryBuilder('deposit')
      .where('deposit.userId = :userId', { userId });

    if (startDate) {
      query.andWhere('deposit.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      query.andWhere('deposit.createdAt <= :endDate', { endDate });
    }

    if (status) {
      query.andWhere('deposit.status = :status', { status });
    }

    query
      .skip((page - 1) * limit)
      .take(limit)
      .leftJoinAndSelect('deposit.user', 'user')
      .leftJoinAndSelect('deposit.wallet', 'wallet');

    return query.getMany();
  }

  async getDepositsByWallet(
    walletId: string,
    startDate?: Date,
    endDate?: Date,
    page: number = 1,
    limit: number = 10,
    status?: DepositStatus, // Optional status parameter
  ): Promise<Deposit[]> {
    const query = this.dataSource
      .getRepository(Deposit)
      .createQueryBuilder('deposit')
      .where('deposit.walletId = :walletId', { walletId });

    if (startDate) {
      query.andWhere('deposit.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      query.andWhere('deposit.createdAt <= :endDate', { endDate });
    }

    if (status) {
      query.andWhere('deposit.status = :status', { status });
    }

    query
      .skip((page - 1) * limit)
      .take(limit)
      .leftJoinAndSelect('deposit.user', 'user')
      .leftJoinAndSelect('deposit.wallet', 'wallet');

    return query.getMany();
  }
}
