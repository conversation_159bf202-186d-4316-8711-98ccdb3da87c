import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDateString,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import { SwapTransactionStatus } from '../entities/swap-transaction.entity';

export class SwapQuotationDto {
  @IsString()
  id: string;

  @IsString()
  userId: string;

  @IsString()
  fromCurrency: string;

  @IsString()
  toCurrency: string;

  @IsString()
  quotedPrice: string;

  @IsString()
  quotedCurrency: string;

  @IsString()
  fromAmount: string;

  @IsString()
  toAmount: string;

  @IsBoolean()
  @IsOptional()
  confirmed?: boolean;

  @IsString()
  expiresAt: string;
}

export class SwapTransactionDto {
  @IsString()
  id: string;

  @IsString()
  userId: string;

  @IsString()
  @IsOptional()
  quotationId?: string;

  @IsString()
  fromCurrency: string;

  @IsString()
  toCurrency: string;

  @IsString()
  fromAmount: string;

  @IsString()
  @IsOptional()
  receivedAmount?: string;

  @IsString()
  @IsOptional()
  executionPrice?: string;

  @IsEnum(SwapTransactionStatus)
  @IsOptional()
  status?: SwapTransactionStatus;
}

export class GetSwapsByUserIdDto {
  @ApiProperty()
  @IsDateString()
  startDate: string;

  @ApiProperty()
  @IsDateString()
  endDate: string;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 10;
}

export class CreateSwapDto {
  @ApiProperty()
  @IsString()
  fromCurrency: string;

  @ApiProperty()
  @IsString()
  toCurrency: string;

  @ApiProperty()
  @IsString()
  fromAmount: string;
}
