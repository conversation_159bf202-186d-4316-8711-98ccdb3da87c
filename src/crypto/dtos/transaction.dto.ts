import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum } from 'class-validator';
import { TransactionType } from '../entities/transactions.entity';

// export class createTransactionDto {
//     @ApiProperty()
//     @IsString()

// }

export class TransactionDto {
  @IsString()
  reference: string | null;

  @IsString()
  currency: string;

  @IsString()
  amount: string;

  @IsString()
  @IsOptional()
  transactionNote: string;

  @IsString()
  @IsOptional()
  narration: string;

  @IsString()
  @IsOptional()
  userId?: string;

  @IsString()
  @IsOptional()
  address?: string;

  @IsString()
  @IsOptional()
  destinationTag?: string;

  @IsString()
  @IsOptional()
  network?: string;

  @IsEnum(TransactionType)
  @IsOptional()
  type?: TransactionType;
}

export class CreateTransactionDto {
  @ApiProperty()
  @IsString()
  amount: string;

  @ApiProperty({
    description: 'For external use and crediting only.',
  })
  @IsString()
  cryptoAddress: string;

  @ApiProperty({ description: 'For external use only.', required: false })
  @IsString()
  @IsOptional()
  destinationTag?: string;

  @ApiProperty()
  @IsString()
  currency: string;

  @ApiProperty({ description: 'Note to send to the address.', required: false })
  @IsString()
  @IsOptional()
  transaction_note?: string;

  @ApiProperty({
    description: 'Any description of the transaction if required.',
    required: false,
  })
  @IsString()
  @IsOptional()
  narration?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  network?: string;
}

export class FundTransactionDto {
  @ApiProperty()
  @IsString()
  amount: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  transaction_note?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  narration?: string;
}

export class WithdrawalTransactionDto {
  @ApiProperty()
  @IsString()
  amount: string;

  @ApiProperty()
  @IsString()
  currency: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  transaction_note?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  narration?: string;
}
