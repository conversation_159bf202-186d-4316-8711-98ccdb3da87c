import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional } from 'class-validator';

export class MarketDataDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty({ description: 'Market name (e.g., BTC_USDT)' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Base unit of the trading pair' })
  @IsString()
  baseUnit: string;

  @ApiProperty({ description: 'Quote unit of the trading pair' })
  @IsString()
  quoteUnit: string;

  @ApiProperty({ description: 'Market price step' })
  @IsNumber()
  @IsOptional()
  priceStep: number;

  @ApiProperty({ description: 'Base precision' })
  @IsNumber()
  basePrecision: number;

  @ApiProperty({ description: 'Quote precision' })
  @IsNumber()
  quotePrecision: number;

  @ApiProperty({ description: 'Price precision' })
  @IsNumber()
  pricePrecision: number;

  @ApiProperty({ description: 'Minimum order size' })
  @IsNumber()
  minimumOrderSize: number;
}

export interface MarketPerformanceData {
  assetName: string;
  id: string;
  name: string;
  baseUnit: string;
  quoteUnit: string;
  priceStep?: number;
  basePrecision: number;
  quotePrecision: number;
  pricePrecision: number;
  minimumOrderSize: number;
  image?: string;
  buy?: string;
  sell?: string;
  low?: string;
  high?: string;
  open?: string;
  last?: string;
  vol?: string;
  priceChangePercent24h?: string;
  lastPrice?: string;
  lowestAsk?: string;
  highestBid?: string;
  baseVolume?: string;
  quoteVolume?: string;
  highestPrice24h?: string;
  lowestPrice24h?: string;
  createdAt?: Date;
  updatedAt?: Date;
}
