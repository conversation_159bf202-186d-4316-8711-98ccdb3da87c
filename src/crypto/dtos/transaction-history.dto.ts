import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsDateString,
  IsNumber,
  Min,
  Max,
  IsUUID,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  TransactionHistoryType,
  TransactionHistoryStatus,
} from '../entities/transaction-history.entity';

export class CreateTransactionHistoryDto {
  @ApiProperty()
  @IsString()
  userId: string;

  @ApiProperty()
  @IsString()
  walletId: string;

  @ApiProperty()
  @IsString()
  reference: string;

  @ApiProperty({ enum: TransactionHistoryType })
  @IsEnum(TransactionHistoryType)
  type: TransactionHistoryType;

  @ApiProperty()
  @IsString()
  currency: string;

  @ApiProperty()
  @IsString()
  amount: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  balanceBefore?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  balanceAfter?: string;

  @ApiProperty({ enum: TransactionHistoryStatus })
  @IsEnum(TransactionHistoryStatus)
  status: TransactionHistoryStatus;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sourceType?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sourceId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sourceReference?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  correlationId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  metadata?: Record<string, any>;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  fee?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  network?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  txid?: string;
}

export class RecordTransactionHistoryDto {
  @ApiProperty()
  @IsString()
  userId: string;

  @ApiProperty()
  @IsString()
  walletId: string;

  @ApiProperty({ enum: TransactionHistoryType })
  @IsEnum(TransactionHistoryType)
  type: TransactionHistoryType;

  @ApiProperty()
  @IsString()
  amount: string;

  @ApiProperty()
  @IsString()
  sourceType: string;

  @ApiProperty()
  @IsString()
  sourceId: string;

  @ApiProperty()
  @IsString()
  sourceReference: string;

  @ApiPropertyOptional()
  @IsOptional()
  metadata?: Record<string, any>;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  fee?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  network?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  txid?: string;
}

export class TransactionHistoryFilterDto {
  @ApiPropertyOptional({ enum: TransactionHistoryType })
  @IsOptional()
  @IsEnum(TransactionHistoryType)
  type?: TransactionHistoryType;

  @ApiPropertyOptional({ enum: TransactionHistoryStatus })
  @IsOptional()
  @IsEnum(TransactionHistoryStatus)
  status?: TransactionHistoryStatus;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sourceType?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sourceReference?: string;
}

export class PaginationDto {
  @ApiPropertyOptional({ default: 1, minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ default: 10, minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;
}

export class TransactionHistoryDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  reference: string;

  @ApiProperty({ enum: TransactionHistoryType })
  type: TransactionHistoryType;

  @ApiProperty()
  currency: string;

  @ApiProperty()
  amount: string;

  @ApiPropertyOptional()
  balanceBefore?: string;

  @ApiPropertyOptional()
  balanceAfter?: string;

  @ApiProperty({ enum: TransactionHistoryStatus })
  status: TransactionHistoryStatus;

  @ApiPropertyOptional()
  sourceType?: string;

  @ApiPropertyOptional()
  sourceId?: string;

  @ApiPropertyOptional()
  sourceReference?: string;

  @ApiPropertyOptional()
  correlationId?: string;

  @ApiPropertyOptional()
  metadata?: Record<string, any>;

  @ApiPropertyOptional()
  description?: string;

  @ApiPropertyOptional()
  fee?: string;

  @ApiPropertyOptional()
  network?: string;

  @ApiPropertyOptional()
  txid?: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  wallet: {
    id: string;
    currency: string;
  };
}

export class PaginatedTransactionHistoryDto {
  @ApiProperty({ type: [TransactionHistoryDto] })
  items: TransactionHistoryDto[];

  @ApiProperty()
  meta: {
    page: number;
    limit: number;
    totalPages: number;
    total: number;
  };
}
