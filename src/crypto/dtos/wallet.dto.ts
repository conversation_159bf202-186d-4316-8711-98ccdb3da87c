import { IsString, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateWalletDto {
  @IsString()
  id: string;

  @IsString()
  currency: string;

  @Transform(({ value }) => parseFloat(value))
  @IsString()
  balance: string;

  @Transform(({ value }) => parseFloat(value))
  @IsString()
  convertedBalance: string;

  @Transform(({ value }) => parseFloat(value))
  @IsString()
  locked: string;

  @Transform(({ value }) => parseFloat(value))
  @IsString()
  staked: string;

  @IsString()
  @IsOptional()
  depositAddress?: string;

  @IsString()
  @IsOptional()
  destinationTag?: string;
}
