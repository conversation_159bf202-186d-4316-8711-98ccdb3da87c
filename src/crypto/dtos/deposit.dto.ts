import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDateString, IsOptional, IsInt, Min, IsEnum } from 'class-validator';
import { DepositStatus } from '../entities/deposits.entity';

export class GetDepositsQueryDto {
  @ApiProperty()
  @IsDateString()
  startDate: string;

  @ApiProperty()
  @IsDateString()
  endDate: string;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 10;

  @ApiProperty()
  @IsOptional()
  @IsEnum(DepositStatus)
  @IsOptional()
  status?: DepositStatus = DepositStatus.ACCEPTED;
}
