// user/dto/create-user.dto.ts
import { IsString, IsEmail, IsNotEmpty, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class CreateUserDto {
  @IsString()
  @IsNotEmpty()
  userId: string;

  @IsString()
  @IsNotEmpty()
  id: string;

  @Transform(({ value }) => value.trim())
  @IsString()
  @IsNotEmpty()
  sn: string;

  @Transform(({ value }) => value.trim().toLowerCase())
  @IsEmail()
  email: string;

  @IsOptional()
  @Transform(({ value }) => value || null)
  reference?: string | null;

  @Transform(({ value }) => value.trim())
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @Transform(({ value }) => value.trim())
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @Transform(({ value }) => value?.trim())
  @IsString()
  @IsOptional()
  displayName?: string;
}

export class CreatePaymentAddressDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  currency: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  network?: string;
}

export class ValidateAddressDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  address: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  currency: string;
}
