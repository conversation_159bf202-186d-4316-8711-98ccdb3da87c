import { Injectable } from '@nestjs/common';
import { RabbitmqService } from '@crednet/utils';
import { Events } from '../../utils/queue';
import { BaseWebhookConsumer } from '../../utils/webhook/base-webhook.consumer';
import { WebhookEventHandlerFactory } from '../../utils/webhook/webhook-event.handler';
import {
  SwapTransactionCompletedEventHandler,
  SwapTransactionFailedEventHandler,
  SwapTransactionReversedEventHandler,
} from './swaps.event-handlers';

@Injectable()
export class SwapsWebhookConsumer extends BaseWebhookConsumer {
  constructor(
    protected readonly rmqService: RabbitmqService,
    private readonly eventHandlerFactory: WebhookEventHandlerFactory,
    private readonly swapCompletedHandler: SwapTransactionCompletedEventHandler,
    private readonly swapFailedHandler: SwapTransactionFailedEventHandler,
    private readonly swapReversedHandler: SwapTransactionReversedEventHandler,
  ) {
    super(rmqService);
  }

  /**
   * Register event handlers for swap events
   */
  protected registerEventHandlers(): void {
    this.eventHandlerFactory.registerHandler(
      Events.SWAP_TRANSACTION_COMPLETED,
      this.swapCompletedHandler,
    );
    this.eventHandlerFactory.registerHandler(
      Events.SWAP_TRANSACTION_FAILED,
      this.swapFailedHandler,
    );
    this.eventHandlerFactory.registerHandler(
      Events.SWAP_TRANSACTION_REVERSED,
      this.swapReversedHandler,
    );

    this.subscribe(Events.SWAP_TRANSACTION_COMPLETED);
    this.subscribe(Events.SWAP_TRANSACTION_FAILED);
    this.subscribe(Events.SWAP_TRANSACTION_REVERSED);
  }

  /**
   * Get the handler for an event
   * @param eventName The event name
   * @returns A function that handles the event
   */
  protected getEventHandler(eventName: string): (data: any) => Promise<void> {
    const handler = this.eventHandlerFactory.getHandler(eventName);
    if (handler) {
      return handler.handle.bind(handler);
    }
    return null;
  }
}
