import { Injectable, Logger } from '@nestjs/common';
import { WebhookEventHandler } from '../../utils/webhook/webhook-event.handler';
import { SwapsService } from './swaps.service';
import { SwapCompletedPortfolioHandler } from '../portfolio/portfolio.event-handlers';

/**
 * Base handler for swap events
 */
@Injectable()
export abstract class BaseSwapEventHandler implements WebhookEventHandler {
  protected readonly logger = new Logger(BaseSwapEventHandler.name);

  constructor(protected readonly swapsService: SwapsService) {}

  abstract handle(data: any): Promise<void>;
}

/**
 * Handler for swap transaction completed events
 */
@Injectable()
export class SwapTransactionCompletedEventHandler extends BaseSwapEventHandler {
  constructor(
    protected readonly swapsService: SwapsService,
    private readonly portfolioHandler: SwapCompletedPortfolioHandler,
  ) {
    super(swapsService);
  }

  async handle(data: any): Promise<void> {
    try {
      await this.swapsService.updateSwapTransactionStatus(data.id, data);

      this.logger.log(
        `Swap transaction completed: ${data.id}, status: ${data.status}`,
      );

      // TODO: Send notification to user when notification service is implemented

      // Trigger portfolio snapshot creation
      try {
        await this.portfolioHandler.handle(data);
      } catch (error) {
        this.logger.error(
          'Error triggering portfolio snapshot for swap completion:',
          error,
        );
        // Don't throw - portfolio snapshot failure shouldn't break swap processing
      }
    } catch (error) {
      this.logger.error(
        `Error handling swap transaction completed event: ${error}`,
      );
    }
  }
}

/**
 * Handler for swap transaction failed events
 */
@Injectable()
export class SwapTransactionFailedEventHandler extends BaseSwapEventHandler {
  async handle(data: any): Promise<void> {
    try {
      await this.swapsService.updateSwapTransactionStatus(data.id, data);

      this.logger.log(
        `Swap transaction failed: ${data.id}, status: ${data.status}`,
      );

      // TODO: Send notification to user when notification service is implemented
    } catch (error) {
      this.logger.error(
        `Error handling swap transaction failed event: ${error}`,
      );
    }
  }
}

/**
 * Handler for swap transaction reversed events
 */
@Injectable()
export class SwapTransactionReversedEventHandler extends BaseSwapEventHandler {
  async handle(data: any): Promise<void> {
    try {
      await this.swapsService.updateSwapTransactionStatus(data.id, data);

      this.logger.log(
        `Swap transaction reversed: ${data.id}, status: ${data.status}`,
      );

      // TODO: Send notification to user when notification service is implemented
    } catch (error) {
      this.logger.error(
        `Error handling swap transaction reversed event: ${error}`,
      );
    }
  }
}
