import { Entity, Column, ManyToOne } from 'typeorm';
import { User } from './user.entity';
import { Currency } from './currency.entity';
import { BaseEntity } from '../../config/repository/base-entity';

@Entity('wallet')
export class Wallet extends BaseEntity {
  @ManyToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
  user: User;

  @ManyToOne(() => Currency, (currency) => currency.id, { onDelete: 'CASCADE' })
  currency: Currency;

  @Column({ type: 'decimal', precision: 20, scale: 8, default: 0 })
  balance: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, default: 0 })
  convertedBalance: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, default: 0 })
  locked: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, default: 0 })
  staked: string;

  @Column({ nullable: true })
  depositAddress: string;

  @Column({ nullable: true })
  destinationTag: string;
}
