import { Entity, Column, ManyToOne } from 'typeorm';
import { User } from './user.entity';
import { Wallet } from './wallet.entity';
import { BaseEntity } from '../../config/repository/base-entity';

export enum TransactionStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  SUCCESS = 'success',
  FAILED = 'failed',
  ABANDONED = 'abandoned',
  EXTERNAL = 'external',
}

export enum WalletType {
  cash = 'cash-wallet',
  credit = 'credit',
}

export enum TransactionType {
  FUND = 'fund',
  WITHDRAWAL = 'withdrawal',
  EXTERNAL = 'external',
}

@Entity('transactions')
export class Transaction extends BaseEntity {
  @ManyToOne(() => Wallet, (wallet) => wallet.id, { onDelete: 'CASCADE' })
  wallet: Wallet;

  @ManyToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
  user: User;

  @Column({ unique: true })
  reference: string;

  @Column()
  currency: string;

  @Column({ nullable: true })
  network: string;

  @Column({
    type: 'enum',
    enum: TransactionType,
    default: TransactionType.EXTERNAL,
  })
  type: TransactionType;

  @Column({ type: 'decimal', precision: 20, scale: 8 })
  amount: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true })
  fee: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true })
  total: string;

  @Column({ nullable: true })
  txid: string;

  @Column({ nullable: true })
  transactionNote: string;

  @Column({ nullable: true })
  narration: string;

  @Column({
    type: 'enum',
    enum: [WalletType.cash, WalletType.credit],
    nullable: true,
  })
  walletType: WalletType;

  @Column({
    type: 'enum',
    enum: TransactionStatus,
    default: TransactionStatus.PENDING,
  })
  status: TransactionStatus;

  @Column({
    type: 'enum',
    enum: TransactionStatus,
    default: TransactionStatus.PENDING,
  })
  paymentStatus: TransactionStatus;

  @Column({ nullable: true })
  reason: string;

  @Column({ nullable: true })
  recipientType: string;

  @Column({ nullable: true })
  recipientAddress: string;

  @Column({ nullable: true })
  recipientDestinationTag: string;

  @Column({ nullable: true })
  recipientName: string;

  @Column({ default: false })
  isRefunded: boolean;

  @Column({ type: 'json', nullable: true })
  meta: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  errors: Record<string, any>;
}
