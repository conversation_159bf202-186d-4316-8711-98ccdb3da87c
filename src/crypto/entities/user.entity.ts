import { Column, <PERSON>ti<PERSON>, OneToMany } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { Wallet } from './wallet.entity';

@Entity('users')
export class User extends BaseEntity {
  @Column({ nullable: false })
  userId: string;

  @Column({ nullable: false })
  firstName: string;

  @Column({ nullable: false })
  lastName: string;

  @Column({ nullable: false })
  sn: string;

  @Column({ nullable: false })
  email: string;

  @Column({ nullable: true })
  reference: string;

  @Column({ nullable: true })
  displayName: string;

  @OneToMany(() => Wallet, (wallet) => wallet.user)
  wallets: Wallet[];
}
