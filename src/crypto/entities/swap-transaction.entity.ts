import { Column, Entity, <PERSON>To<PERSON>ne, OneTo<PERSON>ne, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { User } from './user.entity';
import { BaseEntity } from '../../config/repository/base-entity';
import { SwapQuotation } from './swap-quotation.entity';

export enum SwapTransactionStatus {
  INITIATED = 'initiated',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REVERSED = 'reversed',
}

@Entity('swap_transactions')
export class SwapTransaction extends BaseEntity {
  @ManyToOne(() => User, (user) => user.id)
  user: User;

  @OneToOne(() => SwapQuotation, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn()
  quotation: SwapQuotation;

  @Column()
  fromCurrency: string;

  @Column()
  toCurrency: string;

  @Column()
  fromAmount: string;

  @Column({ nullable: true })
  receivedAmount: string;

  @Column({ nullable: true })
  executionPrice: string;

  @Column({
    type: 'enum',
    enum: SwapTransactionStatus,
    default: SwapTransactionStatus.INITIATED,
  })
  status: SwapTransactionStatus;
}
