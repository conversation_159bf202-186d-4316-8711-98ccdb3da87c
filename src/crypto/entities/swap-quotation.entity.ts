import { Column, Entity, ManyToOne } from 'typeorm';
import { User } from './user.entity';
import { BaseEntity } from '../../config/repository/base-entity';

@Entity('swap_quotations')
export class SwapQuotation extends BaseEntity {
  @ManyToOne(() => User, (user) => user.id)
  user: User;

  @Column()
  fromCurrency: string;

  @Column()
  toCurrency: string;

  @Column()
  quotedPrice: string;

  quotedCurrency: string;

  @Column()
  fromAmount: string;

  @Column()
  toAmount: string;

  @Column({ default: false })
  confirmed: boolean;

  @Column()
  expiresAt: string;
}
