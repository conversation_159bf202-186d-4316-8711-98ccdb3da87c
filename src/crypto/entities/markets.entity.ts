// market-pair.entity.ts
import { Entity, Column } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';

@Entity('market')
export class Market extends BaseEntity {
  @Column()
  name: string;

  @Column()
  baseUnit: string;

  @Column()
  quoteUnit: string;

  @Column({ type: 'int', nullable: true })
  priceStep: number;

  @Column({ type: 'int' })
  basePrecision: number;

  @Column({ type: 'int' })
  quotePrecision: number;

  @Column({ type: 'int' })
  pricePrecision: number;

  @Column({ type: 'decimal', precision: 20, scale: 8 })
  minimumOrderSize: number;
}
