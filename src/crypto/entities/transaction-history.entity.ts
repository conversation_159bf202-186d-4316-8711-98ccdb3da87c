import { Entity, Column, ManyToOne, Index } from 'typeorm';
import { User } from './user.entity';
import { Wallet } from './wallet.entity';
import { BaseEntity } from '../../config/repository/base-entity';

export enum TransactionHistoryType {
  DEPOSIT = 'deposit',
  WITHDRAWAL = 'withdrawal',
  FUND = 'fund',
  TRANSFER = 'transfer',
  ORDER_BUY = 'order_buy',
  ORDER_SELL = 'order_sell',
  SWAP = 'swap',
  REFUND = 'refund',
  EXTERNAL = 'external',
}

export enum TransactionHistoryStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

@Entity('transaction_history')
@Index(['user', 'createdAt'])
@Index(['wallet', 'createdAt'])
@Index(['type', 'createdAt'])
@Index(['status', 'createdAt'])
@Index(['currency', 'createdAt'])
@Index(['sourceType', 'sourceId'])
export class TransactionHistory extends BaseEntity {
  @ManyToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
  user: User;

  @ManyToOne(() => Wallet, (wallet) => wallet.id, { onDelete: 'CASCADE' })
  wallet: Wallet;

  @Column({ unique: true })
  reference: string;

  @Column({
    type: 'enum',
    enum: TransactionHistoryType,
  })
  type: TransactionHistoryType;

  @Column()
  currency: string;

  @Column({ type: 'decimal', precision: 20, scale: 8 })
  amount: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true })
  balanceBefore: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true })
  balanceAfter: string;

  @Column({
    type: 'enum',
    enum: TransactionHistoryStatus,
    default: TransactionHistoryStatus.PENDING,
  })
  status: TransactionHistoryStatus;

  @Column({ nullable: true })
  sourceType: string; // 'order', 'swap', 'transaction', 'deposit'

  @Column({ nullable: true })
  sourceId: string;

  @Column({ nullable: true })
  sourceReference: string;

  @Column({ nullable: true })
  correlationId: string;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ nullable: true })
  description: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true })
  fee: string;

  @Column({ nullable: true })
  network: string;

  @Column({ nullable: true })
  txid: string; // External transaction ID if applicable
}
