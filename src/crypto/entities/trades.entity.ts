import { Entity, Column, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { Orders } from './orders.entity';
import { User } from './user.entity';

@Entity('trades')
export class Trade extends BaseEntity {
  @ManyToOne(() => Orders, (order) => order.trades)
  order: Orders;

  @ManyToOne(() => User, (user) => user.id)
  user: User;

  @Column()
  marketId: string;

  @Column()
  marketBaseUnit: string;

  @Column()
  marketQuoteUnit: string;

  @Column({
    type: 'decimal',
    precision: 20,
    scale: 8,
    nullable: true,
  })
  priceAmount: string;

  @Column()
  priceUnit: string;

  @Column({
    type: 'decimal',
    precision: 20,
    scale: 8,
    nullable: true,
  })
  volumeAmount: string;

  @Column()
  volumeUnit: string;

  @Column({
    type: 'decimal',
    precision: 20,
    scale: 8,
    nullable: true,
  })
  totalAmount: string;

  @Column()
  totalUnit: string;
}
