import { En<PERSON>ty, PrimaryColumn, Column, ManyToMany } from 'typeorm';
import { Currency } from './currency.entity';

@Entity('networks')
export class Network {
  @PrimaryColumn()
  id: string;

  @Column()
  name: string;

  @Column({ default: true })
  depositsEnabled: boolean;

  @Column({ default: true })
  withdrawsEnabled: boolean;

  @ManyToMany(() => Currency, (currency) => currency.networks)
  currencies: Currency[];
}
