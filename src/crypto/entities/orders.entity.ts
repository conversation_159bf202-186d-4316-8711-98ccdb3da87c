import { Column, Entity, ManyToOne, OneToMany } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { User } from './user.entity';
import { Trade } from './trades.entity';

export enum OrderSide {
  BUY = 'buy',
  SELL = 'sell',
}

export enum OrderType {
  LIMIT = 'limit',
  MARKET = 'market',
}

export enum OrderStatus {
  PENDING = 'pending',
  DONE = 'done',
  CANCEL = 'cancel',
  WAIT = 'wait',
}

@Entity('orders')
export class Orders extends BaseEntity {
  @ManyToOne(() => User, (user) => user.id)
  user: User;

  @OneToMany(() => Trade, (trade) => trade.order, { cascade: true })
  trades: Trade[];

  @Column({ unique: true })
  reference: string;

  @Column({ nullable: true })
  marketId: string;

  @Column({ nullable: true })
  baseUnit: string;

  @Column({ nullable: true })
  quoteUnit: string;

  @Column({ type: 'enum', enum: OrderSide })
  side: OrderSide;

  @Column({ type: 'enum', enum: OrderType })
  orderType: OrderType;

  @Column()
  priceUnit: string;

  @Column({ type: 'decimal', precision: 20, scale: 8 })
  priceAmount: string;

  @Column()
  avgPriceUnit: string;

  @Column({
    type: 'decimal',
    precision: 20,
    scale: 8,
    nullable: true,
  })
  avgPriceAmount: string;

  @Column({ nullable: true })
  volumeUnit: string;

  @Column({ type: 'decimal', precision: 20, scale: 8 })
  volumeAmount: string;

  @Column({ nullable: true })
  originVolumeUnit: string;

  @Column({
    type: 'decimal',
    precision: 20,
    scale: 8,
    nullable: true,
  })
  originVolumeAmount: string;

  @Column({ nullable: true })
  executedVolumeUnit: string;

  @Column({
    type: 'decimal',
    precision: 20,
    scale: 8,
    nullable: true,
  })
  executedVolumeAmount: string;

  @Column({ type: 'enum', enum: OrderStatus, default: OrderStatus.PENDING })
  status: OrderStatus;

  @Column({ nullable: true })
  tradesCount: string;

  @Column({ type: 'json', nullable: true })
  meta: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  errors: Record<string, any>;
}
