import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { UsersService } from './users.service';

@Injectable()
export class UsersCron {
  constructor(private readonly usersService: UsersService) {}

  @Cron(CronExpression.EVERY_MINUTE)
  async requeryPaymentAddressGeneration() {
    await this.usersService.requeryPaymentAddressGeneration(1);
  }
}
