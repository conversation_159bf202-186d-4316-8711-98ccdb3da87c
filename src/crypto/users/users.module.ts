import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { UserRepository } from '../repositories/users.repository';
import { QuidaxModule } from '@app/quidax';
import { WalletRepository } from '../repositories/wallet.repository';
import { WalletModule } from '../wallet/wallet.module';
import { CurrencyRepository } from '../repositories/currency.repository';
import { UsersCron } from './users.cron';
import { AddressRepository } from '../repositories/address.repository';

@Module({
  imports: [QuidaxModule, WalletModule],
  controllers: [UsersController],
  providers: [
    UsersService,
    UserRepository,
    WalletRepository,
    CurrencyRepository,
    UsersCron,
    AddressRepository,
  ],
})
export class UsersModule {}
