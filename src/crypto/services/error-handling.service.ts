import { Injectable, Logger } from '@nestjs/common';
import { randomUUID } from 'crypto';

export interface ErrorContext {
  correlationId: string;
  userId?: string;
  operation?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

@Injectable()
export class ErrorHandlingService {
  private readonly logger = new Logger(ErrorHandlingService.name);

  /**
   * Create error context with correlation ID for tracking
   */
  createContext(
    userId?: string,
    operation?: string,
    metadata?: Record<string, any>,
  ): ErrorContext {
    const correlationId = randomUUID();
    const context: ErrorContext = {
      correlationId,
      userId,
      operation,
      timestamp: new Date(),
      metadata,
    };

    this.logger.debug(
      `Created error context: ${correlationId} for user: ${userId}, operation: ${operation}`,
    );

    return context;
  }

  /**
   * Handle external API errors (e.g., Quidax API failures)
   */
  handleExternalApiError(
    error: any,
    context: ErrorContext,
    apiName: string = 'External API',
  ): void {
    const errorDetails = {
      correlationId: context.correlationId,
      userId: context.userId,
      operation: context.operation,
      apiName,
      error: {
        message: error.message || 'Unknown API error',
        status: error.status || error.response?.status,
        data: error.response?.data || error.data,
        stack: error.stack,
      },
      timestamp: context.timestamp,
      metadata: context.metadata,
    };

    this.logger.error(
      `External API Error [${context.correlationId}]: ${apiName} failed for user ${context.userId}`,
      errorDetails,
    );

    // Here you could add additional logic like:
    // - Send to monitoring service (e.g., Sentry)
    // - Store in error tracking database
    // - Send alerts for critical errors
  }

  /**
   * Handle cache errors (Redis failures)
   */
  handleCacheError(
    error: any,
    context: ErrorContext,
    operation: string = 'cache operation',
  ): void {
    const errorDetails = {
      correlationId: context.correlationId,
      userId: context.userId,
      operation: context.operation,
      cacheOperation: operation,
      error: {
        message: error.message || 'Unknown cache error',
        code: error.code,
        stack: error.stack,
      },
      timestamp: context.timestamp,
      metadata: context.metadata,
    };

    this.logger.warn(
      `Cache Error [${context.correlationId}]: ${operation} failed for user ${context.userId}`,
      errorDetails,
    );

    // Cache errors are usually non-critical, so we log as warning
    // The application should continue to function without cache
  }

  /**
   * Handle database errors
   */
  handleDatabaseError(error: any, context: ErrorContext, query?: string): void {
    const errorDetails = {
      correlationId: context.correlationId,
      userId: context.userId,
      operation: context.operation,
      query: query || 'Unknown query',
      error: {
        message: error.message || 'Unknown database error',
        code: error.code,
        detail: error.detail,
        constraint: error.constraint,
        table: error.table,
        column: error.column,
        stack: error.stack,
      },
      timestamp: context.timestamp,
      metadata: context.metadata,
    };

    this.logger.error(
      `Database Error [${context.correlationId}]: Query failed for user ${context.userId}`,
      errorDetails,
    );

    // Database errors are critical and should be escalated
  }

  /**
   * Handle validation errors
   */
  handleValidationError(
    error: any,
    context: ErrorContext,
    validationTarget?: string,
  ): void {
    const errorDetails = {
      correlationId: context.correlationId,
      userId: context.userId,
      operation: context.operation,
      validationTarget,
      error: {
        message: error.message || 'Validation failed',
        constraints: error.constraints,
        property: error.property,
        value: error.value,
      },
      timestamp: context.timestamp,
      metadata: context.metadata,
    };

    this.logger.warn(
      `Validation Error [${context.correlationId}]: ${validationTarget} validation failed for user ${context.userId}`,
      errorDetails,
    );
  }

  /**
   * Handle business logic errors
   */
  handleBusinessLogicError(
    error: any,
    context: ErrorContext,
    businessRule?: string,
  ): void {
    const errorDetails = {
      correlationId: context.correlationId,
      userId: context.userId,
      operation: context.operation,
      businessRule,
      error: {
        message: error.message || 'Business rule violation',
        code: error.code,
        stack: error.stack,
      },
      timestamp: context.timestamp,
      metadata: context.metadata,
    };

    this.logger.warn(
      `Business Logic Error [${context.correlationId}]: ${businessRule} failed for user ${context.userId}`,
      errorDetails,
    );
  }

  /**
   * Handle general errors with context
   */
  handleGenericError(
    error: any,
    context: ErrorContext,
    errorType: string = 'Generic Error',
  ): void {
    const errorDetails = {
      correlationId: context.correlationId,
      userId: context.userId,
      operation: context.operation,
      errorType,
      error: {
        message: error.message || 'Unknown error',
        name: error.name,
        code: error.code,
        stack: error.stack,
      },
      timestamp: context.timestamp,
      metadata: context.metadata,
    };

    this.logger.error(
      `${errorType} [${context.correlationId}]: Error occurred for user ${context.userId}`,
      errorDetails,
    );
  }

  /**
   * Log successful operations for audit trail
   */
  logSuccess(context: ErrorContext, operation: string, result?: any): void {
    const successDetails = {
      correlationId: context.correlationId,
      userId: context.userId,
      operation: context.operation || operation,
      result: result ? JSON.stringify(result) : undefined,
      timestamp: context.timestamp,
      metadata: context.metadata,
    };

    this.logger.log(
      `Success [${context.correlationId}]: ${operation} completed for user ${context.userId}`,
      successDetails,
    );
  }

  /**
   * Extract correlation ID from context for use in responses
   */
  getCorrelationId(context: ErrorContext): string {
    return context.correlationId;
  }
}
