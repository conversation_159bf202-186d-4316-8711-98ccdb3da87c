import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { randomUUID } from 'crypto';
import { AuthData } from '@crednet/authmanager';
import { TransactionHistoryRepository } from '../repositories/transaction-history.repository';
import { WalletRepository } from '../repositories/wallet.repository';
import { UserRepository } from '../repositories/users.repository';
import { ErrorHandlingService, ErrorContext } from './error-handling.service';
import { InternalCacheService } from '@app/internal-cache';
import {
  TransactionHistory,
  TransactionHistoryType,
  TransactionHistoryStatus,
} from '../entities/transaction-history.entity';
import {
  RecordTransactionHistoryDto,
  TransactionHistoryFilterDto,
  PaginationDto,
  TransactionHistoryDto,
  PaginatedTransactionHistoryDto,
} from '../dtos/transaction-history.dto';
import { Pagination } from 'nestjs-typeorm-paginate';

@Injectable()
export class TransactionHistoryService {
  private readonly logger = new Logger(TransactionHistoryService.name);

  constructor(
    private readonly transactionHistoryRepository: TransactionHistoryRepository,
    private readonly walletRepository: WalletRepository,
    private readonly userRepository: UserRepository,
    private readonly errorHandlingService: ErrorHandlingService,
    private readonly cacheService: InternalCacheService,
  ) {}

  /**
   * Record a new transaction history entry
   */
  async recordTransactionHistory(
    data: RecordTransactionHistoryDto,
    correlationId?: string,
  ): Promise<TransactionHistory> {
    const context = this.errorHandlingService.createContext(
      data.userId,
      'recordTransactionHistory',
      { sourceType: data.sourceType, sourceId: data.sourceId },
    );

    if (correlationId) {
      context.correlationId = correlationId;
    }

    try {
      // Get wallet to determine currency and current balance
      const wallet = await this.walletRepository.findOne({
        where: {
          id: data.walletId,
          user: { userId: data.userId },
        },
        relations: ['currency', 'user'],
      });

      if (!wallet) {
        throw new BadRequestException('Wallet not found or access denied');
      }

      const reference = randomUUID();
      const balanceBefore = wallet.balance;

      // Calculate balance after (this is for audit purposes)
      const balanceAfter = this.calculateBalanceAfter(
        balanceBefore,
        data.amount,
        data.type,
      );

      const transactionHistoryData = {
        userId: data.userId,
        walletId: data.walletId,
        reference,
        type: data.type,
        currency: wallet.currency.currencyCode,
        amount: data.amount,
        balanceBefore,
        balanceAfter,
        status: TransactionHistoryStatus.COMPLETED,
        sourceType: data.sourceType,
        sourceId: data.sourceId,
        sourceReference: data.sourceReference,
        correlationId: context.correlationId,
        metadata: data.metadata,
        description: data.description,
        fee: data.fee,
        network: data.network,
        txid: data.txid,
      };

      const transactionHistory =
        await this.transactionHistoryRepository.createTransactionHistory(
          transactionHistoryData,
        );

      // Invalidate cache for this user
      await this.invalidateUserCache(data.userId);

      this.errorHandlingService.logSuccess(
        context,
        'recordTransactionHistory',
        { reference },
      );

      return transactionHistory;
    } catch (error) {
      if (error.code) {
        this.errorHandlingService.handleDatabaseError(error, context);
      } else {
        this.errorHandlingService.handleGenericError(error, context);
      }
      throw error;
    }
  }

  /**
   * Get transaction history for a user with filters and pagination
   */
  async getTransactionHistory(
    auth: AuthData,
    filters: TransactionHistoryFilterDto,
    pagination: PaginationDto,
  ): Promise<PaginatedTransactionHistoryDto> {
    const context = this.errorHandlingService.createContext(
      auth.id.toString(),
      'getTransactionHistory',
      { filters, pagination },
    );

    try {
      // Try to get from cache first
      const cacheKey = this.generateCacheKey(
        'user',
        auth.id.toString(),
        filters,
        pagination,
      );

      const cachedResult =
        await this.cacheService.get<PaginatedTransactionHistoryDto>(cacheKey);
      if (cachedResult) {
        return cachedResult;
      }

      const result =
        await this.transactionHistoryRepository.getTransactionHistoryByUser(
          auth.id.toString(),
          filters,
          pagination,
        );

      const response = this.formatPaginatedResponse(result);

      // Cache the result
      await this.cacheService
        .tags('transaction-history', `user:${auth.id}`)
        .set(cacheKey, response, 300); // 5 minutes TTL

      this.errorHandlingService.logSuccess(context, 'getTransactionHistory', {
        count: result.items.length,
      });

      return response;
    } catch (error) {
      if (error.code) {
        this.errorHandlingService.handleDatabaseError(error, context);
      } else {
        this.errorHandlingService.handleGenericError(error, context);
      }
      throw error;
    }
  }

  /**
   * Get transaction history for a specific wallet
   */
  async getWalletTransactionHistory(
    auth: AuthData,
    walletId: string,
    filters: TransactionHistoryFilterDto,
    pagination: PaginationDto,
  ): Promise<PaginatedTransactionHistoryDto> {
    const context = this.errorHandlingService.createContext(
      auth.id.toString(),
      'getWalletTransactionHistory',
      { walletId, filters, pagination },
    );

    try {
      // Try to get from cache first
      const cacheKey = this.generateCacheKey(
        'wallet',
        walletId,
        filters,
        pagination,
      );

      const cachedResult =
        await this.cacheService.get<PaginatedTransactionHistoryDto>(cacheKey);
      if (cachedResult) {
        return cachedResult;
      }

      const result =
        await this.transactionHistoryRepository.getTransactionHistoryByWallet(
          auth.id.toString(),
          walletId,
          filters,
          pagination,
        );

      const response = this.formatPaginatedResponse(result);

      // Cache the result
      await this.cacheService
        .tags('transaction-history', `user:${auth.id}`, `wallet:${walletId}`)
        .set(cacheKey, response, 300); // 5 minutes TTL

      this.errorHandlingService.logSuccess(
        context,
        'getWalletTransactionHistory',
        { count: result.items.length },
      );

      return response;
    } catch (error) {
      if (error.code) {
        this.errorHandlingService.handleDatabaseError(error, context);
      } else {
        this.errorHandlingService.handleGenericError(error, context);
      }
      throw error;
    }
  }

  /**
   * Get transaction history by reference
   */
  async getTransactionHistoryByReference(
    auth: AuthData,
    reference: string,
  ): Promise<TransactionHistoryDto> {
    const context = this.errorHandlingService.createContext(
      auth.id.toString(),
      'getTransactionHistoryByReference',
      { reference },
    );

    try {
      const transactionHistory =
        await this.transactionHistoryRepository.getTransactionHistoryByReference(
          reference,
        );

      // Verify user has access to this transaction history
      if (transactionHistory.user.userId !== auth.id.toString()) {
        throw new BadRequestException(
          'Access denied to this transaction history',
        );
      }

      const response = this.formatSingleResponse(transactionHistory);

      this.errorHandlingService.logSuccess(
        context,
        'getTransactionHistoryByReference',
        { reference },
      );

      return response;
    } catch (error) {
      if (error.code) {
        this.errorHandlingService.handleDatabaseError(error, context);
      } else {
        this.errorHandlingService.handleGenericError(error, context);
      }
      throw error;
    }
  }

  /**
   * Update transaction history status (for pending transactions)
   */
  async updateTransactionHistoryStatus(
    reference: string,
    status: TransactionHistoryStatus,
    metadata?: Record<string, any>,
    correlationId?: string,
  ): Promise<void> {
    const context = this.errorHandlingService.createContext(
      undefined,
      'updateTransactionHistoryStatus',
      { reference, status },
    );

    if (correlationId) {
      context.correlationId = correlationId;
    }

    try {
      await this.transactionHistoryRepository.updateTransactionHistoryStatus(
        reference,
        status,
        metadata,
      );

      // Get the transaction to invalidate user cache
      const transactionHistory =
        await this.transactionHistoryRepository.getTransactionHistoryByReference(
          reference,
        );
      await this.invalidateUserCache(transactionHistory.user.userId);

      this.errorHandlingService.logSuccess(
        context,
        'updateTransactionHistoryStatus',
        { reference, status },
      );
    } catch (error) {
      if (error.code) {
        this.errorHandlingService.handleDatabaseError(error, context);
      } else {
        this.errorHandlingService.handleGenericError(error, context);
      }
      throw error;
    }
  }

  private calculateBalanceAfter(
    balanceBefore: string,
    amount: string,
    type: TransactionHistoryType,
  ): string {
    const before = parseFloat(balanceBefore);
    const changeAmount = parseFloat(amount);

    // Determine if this is a credit or debit operation
    const creditTypes = [
      TransactionHistoryType.DEPOSIT,
      TransactionHistoryType.FUND,
      TransactionHistoryType.REFUND,
    ];

    const isCredit = creditTypes.includes(type);
    const after = isCredit ? before + changeAmount : before - changeAmount;

    return after.toFixed(8);
  }

  private generateCacheKey(
    type: 'user' | 'wallet',
    id: string,
    filters: TransactionHistoryFilterDto,
    pagination: PaginationDto,
  ): string {
    const filterHash = Buffer.from(JSON.stringify(filters)).toString('base64');
    return `crypto:transaction-history:${type}:${id}:page:${pagination.page}:limit:${pagination.limit}:filters:${filterHash}`;
  }

  private async invalidateUserCache(userId: string): Promise<void> {
    try {
      await this.cacheService.invalidateByTags([
        `user:${userId}`,
        'transaction-history',
      ]);
    } catch (error) {
      this.logger.warn(`Failed to invalidate cache for user ${userId}:`, error);
    }
  }

  private formatSingleResponse(
    transactionHistory: TransactionHistory,
  ): TransactionHistoryDto {
    return {
      id: transactionHistory.id,
      reference: transactionHistory.reference,
      type: transactionHistory.type,
      currency: transactionHistory.currency,
      amount: transactionHistory.amount,
      balanceBefore: transactionHistory.balanceBefore,
      balanceAfter: transactionHistory.balanceAfter,
      status: transactionHistory.status,
      sourceType: transactionHistory.sourceType,
      sourceId: transactionHistory.sourceId,
      sourceReference: transactionHistory.sourceReference,
      correlationId: transactionHistory.correlationId,
      metadata: transactionHistory.metadata,
      description: transactionHistory.description,
      fee: transactionHistory.fee,
      network: transactionHistory.network,
      txid: transactionHistory.txid,
      createdAt: transactionHistory.createdAt,
      updatedAt: transactionHistory.updatedAt,
      wallet: {
        id: transactionHistory.wallet.id,
        currency: transactionHistory.wallet.currency.currencyCode,
      },
    };
  }

  private formatPaginatedResponse(
    result: Pagination<TransactionHistory>,
  ): PaginatedTransactionHistoryDto {
    return {
      items: result.items.map((item) => this.formatSingleResponse(item)),
      meta: result.meta,
    };
  }
}
