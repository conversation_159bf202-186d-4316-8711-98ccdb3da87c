import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { MarketsService } from './markets.service';

@Injectable()
export class MarketCron {
  constructor(private readonly marketService: MarketsService) {}

  @Cron(CronExpression.EVERY_DAY_AT_1AM)
  async updateMarketData() {
    console.log(
      `Starting updateMarketData Cron at` + new Date().toLocaleDateString(),
    );

    await this.marketService.createMarkets();

    console.log(
      `Finished updateMarketData Cron at` + new Date().toLocaleDateString(),
    );
  }

  @Cron(CronExpression.EVERY_HOUR)
  async updateTopGainersAndLosers() {
    console.log(
      `Starting updateTopGainersAndLosers Cron at` +
        new Date().toLocaleDateString(),
    );

    await this.marketService.topGainersJob();
    await this.marketService.topLosersJob();

    console.log(
      `Finished updateTopGainersAndLosers Cron at` +
        new Date().toLocaleDateString(),
    );
  }
}
