import { Module } from '@nestjs/common';
import { MarketsService } from './markets.service';
import { MarketsController } from './markets.controller';
import { MarketRepository } from '../repositories/market.repository';
import { QuidaxModule } from '@app/quidax';
import { MarketCron } from './markets.cron';
import { CurrencyRepository } from '../repositories/currency.repository';

@Module({
  imports: [QuidaxModule],
  controllers: [MarketsController],
  providers: [MarketsService, MarketRepository, MarketCron, CurrencyRepository],
})
export class MarketsModule {}
