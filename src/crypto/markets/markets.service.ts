import { Injectable } from '@nestjs/common';
import { QuidaxService } from '@app/quidax';
import { MarketRepository } from '../repositories/market.repository';
import { MarketDataDto, MarketPerformanceData } from '../dtos/market.dto';
import { RedisService } from '@crednet/utils';
import { CurrencyRepository } from '../repositories/currency.repository';

@Injectable()
export class MarketsService {
  constructor(
    private readonly quidaxService: QuidaxService,
    private readonly marketRepository: MarketRepository,
    private readonly redisService: RedisService,
    private readonly currencyRepository: CurrencyRepository,
  ) {}

  async createMarkets(): Promise<void> {
    try {
      const getMarkets = await this.quidaxService.getCryptoMarkets();

      const markets: MarketDataDto[] = getMarkets.map((market) => ({
        id: market.id,
        name: market.name,
        baseUnit: market.base_unit,
        quoteUnit: market.quote_unit,
        priceStep: market.filters.price_step,
        basePrecision: market.trading_rules.base_precision,
        quotePrecision: market.trading_rules.quote_precision,
        pricePrecision: market.trading_rules.price_precision,
        minimumOrderSize: market.trading_rules.minimum_order_size,
      }));

      await this.marketRepository.createOrUpdateMarkets(markets);
    } catch (error) {
      console.error(error);
    }
  }

  async getMarkets(baseCurrencyName?: string) {
    const markets = await this.marketRepository.getMarkets(baseCurrencyName);

    const storedData = [];
    for (const market of markets) {
      const data = await this.redisService
        .getClient()
        .HGETALL(`crypto:market:data:${market.id}`);

      storedData.push({
        ...market,
        ...data,
      });
    }

    return storedData;
  }

  async getMarketById(id: string) {
    const market = await this.marketRepository.getMarketById(id);

    const getAssets = await this.currencyRepository.findOne({
      where: { currencyCode: market.baseUnit },
    });

    const data = await this.redisService
      .getClient()
      .HGETALL(`crypto:market:data:${id}`);

    return {
      assetName: getAssets.name,
      image: getAssets.image,
      ...market,
      ...data,
    };
  }

  async getMarketsSimple() {
    return await this.marketRepository.getMarketsByQuotes();
  }

  async topGainersJob() {
    try {
      const markets = await this.marketRepository.getMarketsRelativeToUsdt();
      const redisClient = this.redisService.getClient();
      const zsetKey = 'crypto:market:top:gainers';
      await redisClient.DEL(zsetKey);

      const zaddArgs = [];
      for (const market of markets) {
        const getAssets = await this.currencyRepository.findOne({
          where: { currencyCode: market.baseUnit },
        });
        try {
          const data = await redisClient.HGETALL(
            `crypto:market:data:${market.id}`,
          );

          const marketData: MarketPerformanceData = {
            assetName: getAssets.name,
            image: getAssets.image,
            ...market,
            ...data,
          };

          const score = parseFloat(marketData.priceChangePercent24h || '0');
          if (score > 0) {
            zaddArgs.push({ score, value: JSON.stringify(marketData) });
          }
        } catch (redisError) {
          console.error(`Redis error for market ${market.id}:`, redisError);
        }
      }
      await redisClient.ZADD(zsetKey, zaddArgs);
      await redisClient.ZREMRANGEBYRANK(zsetKey, 10, -1);
    } catch (error) {
      console.error('Error in topGainersJob:', error);
      return [];
    }
  }

  async getTopGainers(count = 10): Promise<MarketPerformanceData[]> {
    const redisClient = this.redisService.getClient();
    const zsetKey = 'crypto:market:top:gainers';
    const resultsRaw = await redisClient.sendCommand([
      'ZREVRANGE',
      zsetKey,
      '0',
      String(count - 1),
    ]);
    const results = Array.isArray(resultsRaw) ? resultsRaw : [resultsRaw];
    if (results.length === 0) {
      const markets = await this.marketRepository.getMarketsRelativeToUsdt();
      const marketFull = [];
      for (const market of markets) {
        const getAssets = await this.currencyRepository.findOne({
          where: { currencyCode: market.baseUnit },
        });
        try {
          const data = await redisClient.HGETALL(
            `crypto:market:data:${market.id}`,
          );

          const marketData: MarketPerformanceData = {
            assetName: getAssets.name,
            image: getAssets.image,
            ...market,
            ...data,
          };

          marketFull.push(marketData);
        } catch (redisError) {
          console.error(`Redis error for market ${market.id}:`, redisError);
        }
      }
      return marketFull
        .sort((a, b) => {
          const aScore = parseFloat(a.priceChangePercent24h || '0');
          const bScore = parseFloat(b.priceChangePercent24h || '0');
          return bScore - aScore;
        })
        .slice(0, count);
    }
    return results
      .map((item: string) => {
        try {
          return JSON.parse(item);
        } catch {
          return null;
        }
      })
      .filter(Boolean);
  }

  async topLosersJob() {
    try {
      const markets = await this.marketRepository.getMarketsRelativeToUsdt();
      const redisClient = this.redisService.getClient();
      const zsetKey = 'crypto:market:top:losers';
      await redisClient.DEL(zsetKey);

      const zaddArgs = [];
      for (const market of markets) {
        const getAssets = await this.currencyRepository.findOne({
          where: { currencyCode: market.baseUnit },
        });
        try {
          const data = await redisClient.HGETALL(
            `crypto:market:data:${market.id}`,
          );

          const marketData: MarketPerformanceData = {
            assetName: getAssets.name,
            image: getAssets.image,
            ...market,
            ...data,
          };

          const score = parseFloat(marketData.priceChangePercent24h || '0');
          if (score < 0) {
            zaddArgs.push({ score, value: JSON.stringify(marketData) });
          }
        } catch (redisError) {
          console.error(`Redis error for market ${market.id}:`, redisError);
        }
      }
      await redisClient.ZADD(zsetKey, zaddArgs);
      await redisClient.ZREMRANGEBYRANK(zsetKey, 10, -1);
    } catch (error) {
      console.error('Error in topLosersJob:', error);
      return [];
    }
  }

  async getTopLosers(count = 10): Promise<MarketPerformanceData[]> {
    const redisClient = this.redisService.getClient();
    const zsetKey = 'crypto:market:top:losers';
    const resultsRaw = await redisClient.sendCommand([
      'ZRANGE',
      zsetKey,
      '0',
      String(count - 1),
    ]);
    const results = Array.isArray(resultsRaw) ? resultsRaw : [resultsRaw];
    if (results.length === 0) {
      const markets = await this.marketRepository.getMarketsRelativeToUsdt();
      const marketFull = [];
      for (const market of markets) {
        const getAssets = await this.currencyRepository.findOne({
          where: { currencyCode: market.baseUnit },
        });
        try {
          const data = await redisClient.HGETALL(
            `crypto:market:data:${market.id}`,
          );

          const marketData: MarketPerformanceData = {
            assetName: getAssets.name,
            image: getAssets.image,
            ...market,
            ...data,
          };

          marketFull.push(marketData);
        } catch (redisError) {
          console.error(`Redis error for market ${market.id}:`, redisError);
        }
      }
      return marketFull
        .sort((a, b) => {
          const aScore = parseFloat(a.priceChangePercent24h || '0');
          const bScore = parseFloat(b.priceChangePercent24h || '0');
          return aScore - bScore;
        })
        .slice(0, count);
    }
    return results
      .map((item: string) => {
        try {
          return JSON.parse(item);
        } catch {
          return null;
        }
      })
      .filter(Boolean);
  }
}
