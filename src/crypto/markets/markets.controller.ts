import { Controller, Get, Param, Query } from '@nestjs/common';
import { MarketsService } from './markets.service';
import { MarketPerformanceData } from '../dtos/market.dto';
import { ApiQuery } from '@nestjs/swagger';

@Controller('markets')
export class MarketsController {
  constructor(private readonly cryptoMarketsService: MarketsService) {}

  @Get('/')
  @ApiQuery({
    name: 'baseCurrencyName',
    required: false,
    description: 'Filter currencies by the name of the assest',
  })
  async getMarkets(
    @Query('baseCurrencyName') baseCurrencyName?: string,
  ): Promise<any[]> {
    return this.cryptoMarketsService.getMarkets(baseCurrencyName);
  }

  @Get('/simple')
  async getMarketsSimple(): Promise<any> {
    return this.cryptoMarketsService.getMarketsSimple();
  }

  @Get('/top-gainers')
  async getTopGainers(@Query('count') count = 10): Promise<any> {
    return this.cryptoMarketsService.getTopGainers(count);
  }

  @Get('/top-losers')
  async getTopLosers(@Query('count') count = 10): Promise<any> {
    return this.cryptoMarketsService.getTopLosers(count);
  }

  @Get('/:id')
  async getMarketById(
    @Param('id') id: string,
  ): Promise<MarketPerformanceData | null> {
    return this.cryptoMarketsService.getMarketById(id);
  }
}
