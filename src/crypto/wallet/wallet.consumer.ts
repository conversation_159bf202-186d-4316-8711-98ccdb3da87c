import { Injectable } from '@nestjs/common';
import { RabbitmqService } from '@crednet/utils';
import { Events } from '../../utils/queue';
import { BaseWebhookConsumer } from '../../utils/webhook/base-webhook.consumer';
import { WebhookEventHandlerFactory } from '../../utils/webhook/webhook-event.handler';
import {
  WalletCreatedEventHandler,
  WalletUpdatedEventHandler,
} from './wallet.event-handlers';

@Injectable()
export class WalletConsumer extends BaseWebhookConsumer {
  constructor(
    protected readonly rmqService: RabbitmqService,
    private readonly eventHandlerFactory: WebhookEventHandlerFactory,
    private readonly walletCreatedHandler: WalletCreatedEventHandler,
    private readonly walletUpdatedHandler: WalletUpdatedEventHandler,
  ) {
    super(rmqService);
  }

  /**
   * Register event handlers for wallet events
   */
  protected registerEventHandlers(): void {
    // Register event handlers with the factory
    this.eventHandlerFactory.registerHandler(
      Events.CREATE_WALLET,
      this.walletCreatedHandler,
    );
    this.eventHandlerFactory.registerHandler(
      Events.WALLET_UPDATED,
      this.walletUpdatedHandler,
    );

    // Subscribe to events
    this.subscribe(Events.CREATE_WALLET);
    this.subscribe(Events.WALLET_UPDATED);
  }

  /**
   * Get the handler for an event
   * @param eventName The event name
   * @returns A function that handles the event
   */
  protected getEventHandler(eventName: string): (data: any) => Promise<void> {
    const handler = this.eventHandlerFactory.getHandler(eventName);
    if (handler) {
      return handler.handle.bind(handler);
    }
    return null;
  }
}
