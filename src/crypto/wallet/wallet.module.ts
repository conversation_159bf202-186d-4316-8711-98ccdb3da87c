import { Module } from '@nestjs/common';
import { WalletService } from './wallet.service';
import { WalletController } from './wallet.controller';
import { QuidaxModule } from '@app/quidax';
import { CurrencyRepository } from '../repositories/currency.repository';
import { UserRepository } from '../repositories/users.repository';
import { WalletRepository } from '../repositories/wallet.repository';
import { WalletConsumer } from './wallet.consumer';
import { WebhookModule } from '../../utils/webhook/webhook.module';
import {
  WalletCreatedEventHandler,
  WalletUpdatedEventHandler,
} from './wallet.event-handlers';
import { InternalCacheService } from '../../../libs/internal-cache/src/internal-cache.service';
import { AddressRepository } from '../repositories/address.repository';

@Module({
  imports: [QuidaxModule, WebhookModule],
  controllers: [WalletController],
  providers: [
    WalletService,
    UserRepository,
    CurrencyRepository,
    WalletRepository,
    WalletConsumer,
    WalletCreatedEventHandler,
    WalletUpdatedEventHandler,
    InternalCacheService,
    AddressRepository,
  ],
  exports: [WalletService],
})
export class WalletModule {}
