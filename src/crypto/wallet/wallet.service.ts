import {
  Injectable,
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { WalletRepository } from '../repositories/wallet.repository';
import { Wallet } from '../entities/wallet.entity';
import { QuidaxService } from '@app/quidax';
import { CurrencyRepository } from '../repositories/currency.repository';
import { Currency } from '../entities/currency.entity';
import { InternalCacheService } from '@app/internal-cache';
import { AddressRepository } from '../repositories/address.repository';
import { AddressStatus } from '../entities/address.entity';

@Injectable()
export class WalletService {
  constructor(
    private readonly walletRepository: WalletRepository,
    private readonly quidaxService: QuidaxService,
    private readonly currencyRepository: CurrencyRepository,
    private readonly internalCacheService: InternalCacheService,
    private readonly addressRepository: AddressRepository,
  ) {}

  async createWallet(
    id: string,
    userId: string,
    currency: string,
    address: string,
    destinationTag: string,
  ): Promise<Wallet> {
    try {
      await this.validateCurrency(currency);

      await this.addressRepository.update(
        { id: id },
        {
          status: AddressStatus.SUCCESS,
          address: address,
          destinationTag: destinationTag,
        },
      );

      const getWallet = await this.walletRepository.getUserWalletByCurrency(
        userId,
        currency,
      );
      if (getWallet) return getWallet;

      const result = await this.quidaxService.fetchWallet(userId, currency);

      return await this.walletRepository.createWallet(
        {
          id: result.id,
          currency: result.currency,
          balance: result.balance,
          depositAddress: result.deposit_address,
          convertedBalance: result.converted_balance,
          locked: result.locked,
          staked: result.staked,
          destinationTag: result.destination_tag,
        },
        userId,
      );
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      console.error('Error creating wallet:', error);
      throw new InternalServerErrorException(
        'An error occurred while creating the wallet',
      );
    }
  }

  async validateCurrency(currency: string): Promise<void> {
    try {
      const currencies =
        await this.currencyRepository.findAllCurrencies(currency);
      if (currencies.length === 0) {
        throw new BadRequestException('Invalid currency');
      }
    } catch (error) {
      console.error('Error validating currency:', error);
      throw new InternalServerErrorException(
        'An error occurred while validating the currency',
      );
    }
  }

  async getUserWallet(walletId: string): Promise<Wallet> {
    try {
      const wallet = await this.walletRepository.getUserWallet(walletId);
      if (!wallet) {
        throw new NotFoundException('Wallet not found');
      }
      return wallet;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error fetching wallet:', error);
      throw new InternalServerErrorException(
        'An error occurred while fetching the wallet',
      );
    }
  }

  async getAllUserActiveWallets(userId: string): Promise<Wallet[]> {
    try {
      return await this.walletRepository.getAllUserActiveWallets(userId);
    } catch (error) {
      console.error('Error fetching user active wallets:', error);
      throw new InternalServerErrorException(
        'An error occurred while fetching user active wallets',
      );
    }
  }

  async findAllCurrencies(name?: string): Promise<Currency[]> {
    // Check if the data is in the cache
    try {
      const cachedCurrencies =
        await this.internalCacheService.getAllFromCollection<Currency>(
          'crypto:tag:currencies',
        );
      if (cachedCurrencies.length > 0 && !name) {
        return cachedCurrencies;
      }

      // If not in cache, query the database
      const currencies = await this.currencyRepository.findAllCurrencies(name);

      return currencies;
    } catch (error) {
      console.error('Error fetching currencies:', error);
      throw new InternalServerErrorException(
        'An error occurred while fetching currencies',
      );
    }
  }

  async updateWallet(
    walletId: string,
    updateData: Partial<Wallet>,
  ): Promise<Wallet> {
    try {
      const wallet = await this.walletRepository.getUserWallet(walletId);
      if (!wallet) {
        throw new NotFoundException('Wallet not found');
      }

      const updatedWallet = await this.walletRepository.updateWallet(
        walletId,
        updateData,
      );

      return updatedWallet;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error updating wallet:', error);
      throw new InternalServerErrorException(
        'An error occurred while updating the wallet',
      );
    }
  }

  async handleWalletUpdatedEvent(data: any): Promise<Wallet> {
    try {
      const {
        currency,
        balance,
        locked,
        staked,
        user,
        converted_balance,
        deposit_address,
        destination_tag,
      } = data;

      const wallet = await this.walletRepository.findOne({
        where: {
          user: { id: user.id },
          currency: { currencyCode: currency },
        },
        relations: ['user', 'currency'],
      });

      if (!wallet) {
        let fetchWallet: any;
        try {
          fetchWallet = await this.quidaxService.fetchWallet(user.id, currency);
        } catch (error) {
          if (
            error?.response?.status === 'error' &&
            error?.response?.data?.code === 'E0104'
          ) {
            fetchWallet = await this.quidaxService.fetchWallet('me', currency);
          } else {
            throw error;
          }
        }

        return await this.walletRepository.createWallet(
          {
            id: fetchWallet.id,
            currency: fetchWallet.currency,
            balance: fetchWallet.balance,
            depositAddress: fetchWallet.deposit_address,
            convertedBalance: fetchWallet.converted_balance,
            locked: fetchWallet.locked,
            staked: fetchWallet.staked,
            destinationTag: fetchWallet.destination_tag,
          },
          user.id,
        );
      }

      wallet.balance = balance;
      wallet.locked = locked;
      wallet.staked = staked;
      wallet.convertedBalance = converted_balance;

      if (deposit_address !== undefined) {
        wallet.depositAddress = deposit_address;
      }
      if (destination_tag !== undefined) {
        wallet.destinationTag = destination_tag;
      }

      return await this.walletRepository.save(wallet);
    } catch (error) {
      console.error('Error updating wallet:', error);
      throw new InternalServerErrorException(
        'An error occurred while updating the wallet',
      );
    }
  }
}
