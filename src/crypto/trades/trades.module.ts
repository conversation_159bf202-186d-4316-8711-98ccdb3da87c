import { Module } from '@nestjs/common';
import { TradesService } from './trades.service';
import { TradesController } from './trades.controller';
import { QuidaxModule } from '@app/quidax';
import { TradeRepository } from '../repositories/trades.repository';

@Module({
  imports: [QuidaxModule],
  controllers: [TradesController],
  providers: [TradesService, TradeRepository],
})
export class TradesModule {}
