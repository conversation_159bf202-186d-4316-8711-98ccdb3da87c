import { Controller, Get, Query, Param, UseGuards } from '@nestjs/common';
import { TradesService } from './trades.service';
import { Trade } from '../entities/trades.entity';
import { AuthData, GetAuthData, JwtAuthGuard } from '@crednet/authmanager';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

@Controller('trades')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('trades')
export class TradesController {
  constructor(private readonly tradesService: TradesService) {}

  @Get('recent-trades')
  async fetchRecentTradesForAMarket(
    @Query('baseCurrency') baseCurrency: string,
    @Query('quoteCurrency') quoteCurrency: string,
  ): Promise<any> {
    return this.tradesService.fetchRecentTradesForAMarket(
      baseCurrency,
      quoteCurrency,
    );
  }

  @Get('users/:marketId')
  async fetchAllTradesForUserForAMarketPair(
    @GetAuthData() auth: AuthData,
    @Param('marketId') marketId: string,
  ): Promise<Trade[]> {
    return this.tradesService.fetchAllTradesForUserForAMarketPair(
      auth,
      marketId,
    );
  }
}
