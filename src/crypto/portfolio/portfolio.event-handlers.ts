import { Injectable, Logger } from '@nestjs/common';
import { WebhookEventHandler } from '../../utils/webhook/webhook-event.handler';
import { PortfolioSnapshotService } from './portfolio-snapshot.service';

/**
 * Base handler for portfolio-related events
 */
@Injectable()
export abstract class BasePortfolioEventHandler implements WebhookEventHandler {
  protected readonly logger = new Logger(BasePortfolioEventHandler.name);

  constructor(
    protected readonly portfolioSnapshotService: PortfolioSnapshotService,
  ) {}

  abstract handle(data: any): Promise<void>;

  /**
   * Extract user ID from various event data structures
   */
  protected extractUserId(data: any): string | null {
    // Try different possible locations for user ID
    if (data.user?.id) return data.user.id;
    if (data.user?.userId) return data.user.userId;
    if (data.userId) return data.userId;
    if (data.user_id) return data.user_id;

    return null;
  }
}

/**
 * <PERSON>ler for order completion events
 * Triggers portfolio snapshot when orders are completed
 */
@Injectable()
export class OrderCompletedPortfolioHandler extends BasePortfolioEventHandler {
  async handle(data: any): Promise<void> {
    try {
      const userId = this.extractUserId(data);
      if (!userId) {
        this.logger.warn('No user ID found in order completion event data');
        return;
      }

      this.logger.log(
        `Creating portfolio snapshot for order completion, user: ${userId}`,
      );
      await this.portfolioSnapshotService.createEventTriggeredSnapshot(
        userId,
        'ORDER_DONE',
      );
    } catch (error) {
      this.logger.error(
        'Error handling order completion portfolio event:',
        error,
      );
    }
  }
}

/**
 * Handler for successful deposit events
 * Triggers portfolio snapshot when deposits are completed
 */
@Injectable()
export class DepositSuccessPortfolioHandler extends BasePortfolioEventHandler {
  async handle(data: any): Promise<void> {
    try {
      const userId = this.extractUserId(data);
      if (!userId) {
        this.logger.warn('No user ID found in deposit success event data');
        return;
      }

      this.logger.log(
        `Creating portfolio snapshot for deposit success, user: ${userId}`,
      );
      await this.portfolioSnapshotService.createEventTriggeredSnapshot(
        userId,
        'DEPOSIT_SUCCESSFUL',
      );
    } catch (error) {
      this.logger.error(
        'Error handling deposit success portfolio event:',
        error,
      );
    }
  }
}

/**
 * Handler for successful withdrawal events
 * Triggers portfolio snapshot when withdrawals are completed
 */
@Injectable()
export class WithdrawalSuccessPortfolioHandler extends BasePortfolioEventHandler {
  async handle(data: any): Promise<void> {
    try {
      const userId = this.extractUserId(data);
      if (!userId) {
        this.logger.warn('No user ID found in withdrawal success event data');
        return;
      }

      this.logger.log(
        `Creating portfolio snapshot for withdrawal success, user: ${userId}`,
      );
      await this.portfolioSnapshotService.createEventTriggeredSnapshot(
        userId,
        'WITHDRAWAL_SUCCESS',
      );
    } catch (error) {
      this.logger.error(
        'Error handling withdrawal success portfolio event:',
        error,
      );
    }
  }
}

/**
 * Handler for swap completion events
 * Triggers portfolio snapshot when swaps are completed
 */
@Injectable()
export class SwapCompletedPortfolioHandler extends BasePortfolioEventHandler {
  async handle(data: any): Promise<void> {
    try {
      const userId = this.extractUserId(data);
      if (!userId) {
        this.logger.warn('No user ID found in swap completion event data');
        return;
      }

      this.logger.log(
        `Creating portfolio snapshot for swap completion, user: ${userId}`,
      );
      await this.portfolioSnapshotService.createEventTriggeredSnapshot(
        userId,
        'SWAP_TRANSACTION_COMPLETED',
      );
    } catch (error) {
      this.logger.error(
        'Error handling swap completion portfolio event:',
        error,
      );
    }
  }
}

/**
 * Handler for DCA execution events
 * Triggers portfolio snapshot when DCA strategies are executed
 */
@Injectable()
export class DcaExecutionPortfolioHandler extends BasePortfolioEventHandler {
  async handle(data: any): Promise<void> {
    try {
      const userId = this.extractUserId(data);
      if (!userId) {
        this.logger.warn('No user ID found in DCA execution event data');
        return;
      }

      this.logger.log(
        `Creating portfolio snapshot for DCA execution, user: ${userId}`,
      );
      await this.portfolioSnapshotService.createEventTriggeredSnapshot(
        userId,
        'DCA_EXECUTION',
      );
    } catch (error) {
      this.logger.error('Error handling DCA execution portfolio event:', error);
    }
  }
}
