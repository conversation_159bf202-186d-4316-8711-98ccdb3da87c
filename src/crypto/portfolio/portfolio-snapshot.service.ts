import { Injectable, Logger } from '@nestjs/common';
import { WalletRepository } from '../repositories/wallet.repository';
import { UserRepository } from '../repositories/users.repository';
import { InternalCacheService } from '@app/internal-cache';
import { RedisService } from '@crednet/utils';
import { Wallet } from '../entities/wallet.entity';
import {
  AssetSnapshot,
  PortfolioSnapshot,
} from '../entities/portfolio-snapshot.entity';
import { PortfolioRepository } from '../repositories/portfolio.repository';

@Injectable()
export class PortfolioSnapshotService {
  private readonly logger = new Logger(PortfolioSnapshotService.name);

  constructor(
    private readonly portfolioRepository: PortfolioRepository,
    private readonly walletRepository: WalletRepository,
    private readonly userRepository: UserRepository,
    private readonly cacheService: InternalCacheService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * Create a portfolio snapshot for a specific user
   */
  async createUserSnapshot(userId: string): Promise<void> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        this.logger.warn(`User not found for snapshot creation: ${userId}`);
        return;
      }

      const wallets = await this.walletRepository.getUserWallets(user.id);
      if (wallets.length === 0) {
        this.logger.debug(
          `No wallets found for user ${userId}, skipping snapshot`,
        );
        return;
      }

      const totalValue = this.calculateTotalValue(wallets);

      const assets = await this.createAssetSnapshots(wallets);

      const performanceData = await this.calculatePerformanceData(
        user.id,
        totalValue,
      );

      await this.portfolioRepository.createSnapshot(
        user,
        totalValue.toString(),
        assets,
        performanceData,
      );

      await this.invalidatePortfolioCache(userId);

      this.logger.log(`Portfolio snapshot created for user ${userId}`);
    } catch (error) {
      this.logger.error('Error creating portfolio snapshot:', error);
    }
  }

  /**
   * Create snapshots for all users with active portfolios
   */
  async createSnapshotsForAllUsers(): Promise<void> {
    try {
      this.logger.log('Starting daily portfolio snapshots for all users');

      const usersWithWallets =
        await this.userRepository.getUsersWithActiveWallets();

      this.logger.log(
        `Found ${usersWithWallets.length} users with active portfolios`,
      );

      const batchSize = 10;
      for (let i = 0; i < usersWithWallets.length; i += batchSize) {
        const batch = usersWithWallets.slice(i, i + batchSize);

        await Promise.allSettled(
          batch.map((user) => this.createUserSnapshot(user.id)),
        );

        if (i + batchSize < usersWithWallets.length) {
          await this.delay(1000); // 1 second delay
        }
      }

      this.logger.log('Completed daily portfolio snapshots for all users');
    } catch (error) {
      this.logger.error('Error creating daily portfolio snapshots:', error);
    }
  }

  /**
   * Create snapshot triggered by significant portfolio change
   */
  async createEventTriggeredSnapshot(
    userId: string,
    eventType: string,
  ): Promise<void> {
    try {
      if (!this.shouldCreateEventSnapshot(eventType)) {
        return;
      }

      const recentSnapshot = await this.portfolioRepository.findOne({
        where: {
          user: { userId },
          snapshotDate: new Date(Date.now() - 60 * 60 * 1000),
        },
        order: { snapshotDate: 'DESC' },
      });

      if (recentSnapshot) {
        this.logger.debug(
          `Recent snapshot exists for user ${userId}, skipping event snapshot`,
        );
        return;
      }

      await this.createUserSnapshot(userId);
      this.logger.log(
        `Event-triggered snapshot created for user ${userId}, event: ${eventType}`,
      );
    } catch (error) {
      this.logger.error('Error creating event-triggered snapshot:', error);
    }
  }

  private calculateTotalValue(wallets: Wallet[]): number {
    return wallets.reduce((total, wallet) => {
      return total + parseFloat(wallet.convertedBalance || '0');
    }, 0);
  }

  private async createAssetSnapshots(
    wallets: Wallet[],
  ): Promise<AssetSnapshot[]> {
    const assets: AssetSnapshot[] = [];

    for (const wallet of wallets) {
      const balance = parseFloat(wallet.balance || '0');

      if (balance > 0) {
        const priceChange24h = await this.get24hPriceChange(
          wallet.currency.currencyCode,
        );

        assets.push({
          currency: wallet.currency.currencyCode,
          balance: wallet.balance,
          value: wallet.convertedBalance,
          percentage: 0,
          priceChange24hforNgnMarketPair: priceChange24h.ngn || 0,
          priceChange24hforUsdtMarketPair: priceChange24h.usdt || 0,
        });
      }
    }

    const totalValue = assets.reduce(
      (sum, asset) => sum + parseFloat(asset.value),
      0,
    );
    if (totalValue > 0) {
      assets.forEach((asset) => {
        asset.percentage = (parseFloat(asset.value) / totalValue) * 100;
      });
    }

    return assets;
  }

  private async calculatePerformanceData(
    userId: string,
    currentValue: number,
  ): Promise<any> {
    try {
      const previousSnapshots = await this.portfolioRepository.find({
        where: { user: { userId } },
        order: { snapshotDate: 'DESC' },
        take: 30,
      });

      if (previousSnapshots.length === 0) {
        return {
          dayChange: null,
          dayChangePercent: null,
          weekChange: null,
          weekChangePercent: null,
          monthChange: null,
          monthChangePercent: null,
          profitLoss: null,
          profitLossPercent: null,
        };
      }

      const daySnapshot = this.findSnapshotByAge(previousSnapshots, 1);
      const weekSnapshot = this.findSnapshotByAge(previousSnapshots, 7);
      const monthSnapshot = this.findSnapshotByAge(previousSnapshots, 30);
      const oldestSnapshot = previousSnapshots[previousSnapshots.length - 1];

      return {
        dayChange: daySnapshot
          ? this.calculateChange(
              currentValue,
              parseFloat(daySnapshot.totalValue),
            )
          : null,
        dayChangePercent: daySnapshot
          ? this.calculateChangePercent(
              currentValue,
              parseFloat(daySnapshot.totalValue),
            )
          : null,
        weekChange: weekSnapshot
          ? this.calculateChange(
              currentValue,
              parseFloat(weekSnapshot.totalValue),
            )
          : null,
        weekChangePercent: weekSnapshot
          ? this.calculateChangePercent(
              currentValue,
              parseFloat(weekSnapshot.totalValue),
            )
          : null,
        monthChange: monthSnapshot
          ? this.calculateChange(
              currentValue,
              parseFloat(monthSnapshot.totalValue),
            )
          : null,
        monthChangePercent: monthSnapshot
          ? this.calculateChangePercent(
              currentValue,
              parseFloat(monthSnapshot.totalValue),
            )
          : null,
        profitLoss: oldestSnapshot
          ? this.calculateChange(
              currentValue,
              parseFloat(oldestSnapshot.totalValue),
            )
          : null,
        profitLossPercent: oldestSnapshot
          ? this.calculateChangePercent(
              currentValue,
              parseFloat(oldestSnapshot.totalValue),
            )
          : null,
      };
    } catch (error) {
      this.logger.warn(
        `Failed to calculate performance data for user ${userId}:`,
        error,
      );
      return {};
    }
  }

  private findSnapshotByAge(
    snapshots: PortfolioSnapshot[],
    daysAgo: number,
  ): PortfolioSnapshot | null {
    const targetDate = new Date();
    targetDate.setDate(targetDate.getDate() - daysAgo);

    return snapshots.find((snapshot) => {
      const snapshotDate = new Date(snapshot.snapshotDate);
      const diffDays = Math.abs(
        (targetDate.getTime() - snapshotDate.getTime()) / (1000 * 60 * 60 * 24),
      );
      return diffDays <= 1; // Within 1 day tolerance
    });
  }

  private calculateChange(current: number, previous: number): string {
    return (current - previous).toFixed(2);
  }

  private calculateChangePercent(current: number, previous: number): string {
    if (previous === 0) return '0';
    return (((current - previous) / previous) * 100).toFixed(2);
  }

  private async get24hPriceChange(currency: string): Promise<{
    usdt?: number;
    ngn?: number;
  }> {
    try {
      let usdt: number;
      let ngn: number;
      const marketPair = [currency + 'ngn', currency + 'usdt'];

      if (currency !== 'usdt') {
        marketPair.forEach(async (item) => {
          const market = await this.redisService
            .getClient()
            .HGETALL(`crypto:market:data:${item}`);
          if (item.includes('usdt')) {
            usdt = parseFloat(market.priceChangePercent24h) || 0;
          } else {
            ngn = parseFloat(market.priceChangePercent24h) || 0;
          }
        });
      } else {
        const market = await this.redisService
          .getClient()
          .HGETALL(`crypto:market:data:usdtngn`);
        ngn = parseFloat(market.priceChangePercent24h) || 0;
        usdt = 0;
      }
      return {
        usdt,
        ngn,
      };
    } catch (error) {
      this.logger.warn(
        `Failed to get 24h price change for ${currency}:`,
        error,
      );
      return {
        usdt: 0,
        ngn: 0,
      };
    }
  }

  private shouldCreateEventSnapshot(eventType: string): boolean {
    const significantEvents = [
      'ORDER_DONE',
      'DEPOSIT_SUCCESSFUL',
      'WITHDRAWAL_SUCCESS',
      'SWAP_TRANSACTION_COMPLETED',
    ];
    return significantEvents.includes(eventType);
  }

  private async invalidatePortfolioCache(userId: string): Promise<void> {
    try {
      const overviewKey = `crypto:portfolio:overview:${userId}`;
      const performanceKey = `crypto:portfolio:performance:${userId}`;

      await Promise.all([
        this.cacheService.delete(overviewKey),
        this.cacheService.delete(performanceKey),
      ]);
    } catch (error) {
      this.logger.warn(`Failed to invalidate cache for user ${userId}:`, error);
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
