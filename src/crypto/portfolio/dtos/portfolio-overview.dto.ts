import { ApiProperty } from '@nestjs/swagger';

export class PortfolioOverviewDto {
  @ApiProperty({ description: 'Total portfolio value in USD' })
  totalValue: string;

  @ApiProperty({ description: 'Number of different assets in portfolio' })
  assetCount: number;

  @ApiProperty({ description: 'Asset allocation breakdown' })
  assetAllocation: Array<{
    currency: string;
    percentage: number;
    value: string;
    balance: string;
  }>;

  @ApiProperty({ description: 'Individual wallet details' })
  wallets: Array<{
    currency: string;
    balance: string;
    convertedBalance: string;
  }>;

  @ApiProperty({ description: 'Recent transactions' })
  recentTransactions: Array<{
    id: string;
    type: string;
    currency: string;
    amount: string;
    timestamp: Date;
  }>;
}
