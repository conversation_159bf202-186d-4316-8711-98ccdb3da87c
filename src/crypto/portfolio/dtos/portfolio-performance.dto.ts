import { ApiProperty } from '@nestjs/swagger';

export class PortfolioPerformanceDto {
  @ApiProperty({ description: 'Current total portfolio value in USD' })
  currentValue: string;

  @ApiProperty({ description: '24h change in value' })
  dayChange: string;

  @ApiProperty({ description: '24h change percentage' })
  dayChangePercent: string;

  @ApiProperty({ description: '7d change in value' })
  weekChange: string;

  @ApiProperty({ description: '7d change percentage' })
  weekChangePercent: string;

  @ApiProperty({ description: '30d change in value' })
  monthChange: string;

  @ApiProperty({ description: '30d change percentage' })
  monthChangePercent: string;

  @ApiProperty({ description: 'Total profit/loss since inception' })
  totalProfitLoss: string;

  @ApiProperty({ description: 'Total profit/loss percentage since inception' })
  totalProfitLossPercent: string;

  @ApiProperty({ description: 'Best performing asset' })
  bestPerformer: {
    currency: string;
    change: string;
    changePercent: string;
  } | null;

  @ApiProperty({ description: 'Worst performing asset' })
  worstPerformer: {
    currency: string;
    change: string;
    changePercent: string;
  } | null;

  @ApiProperty({ description: 'Historical performance data points' })
  historicalData: Array<{
    date: Date;
    value: string;
    change: string;
    changePercent: string;
  }>;

  @ApiProperty({ description: 'Performance metrics by time period' })
  performanceMetrics: {
    day: {
      startValue: string;
      endValue: string;
      change: string;
      changePercent: string;
    } | null;
    week: {
      startValue: string;
      endValue: string;
      change: string;
      changePercent: string;
    } | null;
    month: {
      startValue: string;
      endValue: string;
      change: string;
      changePercent: string;
    } | null;
  };
}
