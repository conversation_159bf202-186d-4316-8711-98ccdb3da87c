import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { PortfolioSnapshotService } from './portfolio-snapshot.service';

@Injectable()
export class PortfolioSnapshotCron {
  private readonly logger = new Logger(PortfolioSnapshotCron.name);

  constructor(
    private readonly portfolioSnapshotService: PortfolioSnapshotService,
  ) {}

  /**
   * Create daily portfolio snapshots for all users
   * Runs every day at 1:00 AM UTC
   */
  @Cron('0 1 * * *', {
    name: 'daily-portfolio-snapshots',
    timeZone: 'UTC',
  })
  async createDailySnapshots() {
    this.logger.log('Starting daily portfolio snapshots job');

    try {
      await this.portfolioSnapshotService.createSnapshotsForAllUsers();
      this.logger.log('Daily portfolio snapshots job completed successfully');
    } catch (error) {
      this.logger.error('Daily portfolio snapshots job failed:', error);
    }
  }

  /**
   * Create hourly snapshots for users with recent activity
   * Runs every hour at minute 0
   * This is optional and can be enabled for more granular data
   */
  // @Cron('0 * * * *', {
  //   name: 'hourly-portfolio-snapshots',
  //   timeZone: 'UTC',
  // })
  // async createHourlySnapshots() {
  //   this.logger.log('Starting hourly portfolio snapshots for active users');

  //   try {
  //     // This would create snapshots only for users with recent activity
  //     // Implementation would filter users based on recent transactions/orders
  //     this.logger.log('Hourly portfolio snapshots completed');
  //   } catch (error) {
  //     this.logger.error('Hourly portfolio snapshots failed:', error);
  //   }
  // }
}
