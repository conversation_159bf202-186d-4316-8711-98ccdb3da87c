import { Module } from '@nestjs/common';
import { PortfolioService } from './portfolio.service';
import { PortfolioController } from './portfolio.controller';
import { PortfolioSnapshotService } from './portfolio-snapshot.service';
import { PortfolioSnapshotCron } from './portfolio-snapshot.cron';
import { WalletRepository } from '../repositories/wallet.repository';
import { TransactionRepository } from '../repositories/transaction.repository';
import { QuidaxModule } from '@app/quidax';
import { UserRepository } from '../repositories/users.repository';
import { CurrencyRepository } from '../repositories/currency.repository';
import { InternalCacheModule } from '@app/internal-cache';
import { InternalCacheService } from '@app/internal-cache';
import {
  OrderCompletedPortfolioHandler,
  DepositSuccessPortfolioHandler,
  WithdrawalSuccessPortfolioHandler,
  SwapCompletedPortfolioHandler,
  DcaExecutionPortfolioHandler,
} from './portfolio.event-handlers';
import { PortfolioRepository } from '../repositories/portfolio.repository';

@Module({
  imports: [QuidaxModule, InternalCacheModule],
  controllers: [PortfolioController],
  providers: [
    PortfolioService,
    PortfolioSnapshotService,
    PortfolioSnapshotCron,
    PortfolioRepository,
    WalletRepository,
    TransactionRepository,
    UserRepository,
    CurrencyRepository,
    InternalCacheService,
    OrderCompletedPortfolioHandler,
    DepositSuccessPortfolioHandler,
    WithdrawalSuccessPortfolioHandler,
    SwapCompletedPortfolioHandler,
    DcaExecutionPortfolioHandler,
  ],
  exports: [
    PortfolioService,
    PortfolioSnapshotService,
    OrderCompletedPortfolioHandler,
    DepositSuccessPortfolioHandler,
    WithdrawalSuccessPortfolioHandler,
    SwapCompletedPortfolioHandler,
    DcaExecutionPortfolioHandler,
  ],
})
export class PortfolioModule {}
