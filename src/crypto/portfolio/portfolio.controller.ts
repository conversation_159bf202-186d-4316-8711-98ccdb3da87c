import { Controller, Get, UseGuards } from '@nestjs/common';
import { PortfolioService } from './portfolio.service';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard, GetAuthData } from '@crednet/authmanager';
import { AuthData } from '@crednet/authmanager';
import { PortfolioOverviewDto } from './dtos/portfolio-overview.dto';
import { PortfolioPerformanceDto } from './dtos/portfolio-performance.dto';

@ApiTags('Portfolio')
@Controller('portfolio')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
export class PortfolioController {
  constructor(private readonly portfolioService: PortfolioService) {}

  @Get('overview')
  @ApiOperation({
    summary: 'Get portfolio overview',
    description:
      "Returns the user's portfolio overview including total value, asset allocation, and historical performance.",
  })
  @ApiResponse({
    status: 200,
    description: 'Portfolio overview retrieved successfully',
    type: PortfolioOverviewDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'User not found' })
  getPortfolioOverview(@GetAuthData() auth: AuthData) {
    auth.id = 82;
    return this.portfolioService.getPortfolioOverview(auth);
  }

  @Get('performance')
  @ApiOperation({
    summary: 'Get portfolio performance',
    description:
      "Returns the user's portfolio performance metrics including gains/losses, historical data, and performance over different time periods.",
  })
  @ApiResponse({
    status: 200,
    description: 'Portfolio performance retrieved successfully',
    type: PortfolioPerformanceDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'User not found' })
  getPortfolioPerformance(@GetAuthData() auth: AuthData) {
    auth.id = 82;
    return this.portfolioService.getPortfolioPerformance(auth);
  }
}
