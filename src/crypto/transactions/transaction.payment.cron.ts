import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { TransactionsService } from './transactions.service';

@Injectable()
export class TransactionPaymentCron {
  constructor(private readonly transactionService: TransactionsService) {}

  @Cron(CronExpression.EVERY_5_MINUTES)
  async requeryProcessing() {
    await this.transactionService.requeryProcessing(1);
  }

  @Cron(CronExpression.EVERY_10_MINUTES)
  async reprocessPendingProcessing() {
    await this.transactionService.reprocessPendingProcessing(1);
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async requeryPendingPayment() {
    await this.transactionService.requeryPendingPayment(1);
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async handleRefund() {
    await this.transactionService.handleRefund(1);
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async requeryPendingWithdrawal() {
    await this.transactionService.requeryPendingWithdrawal(1);
  }
}
