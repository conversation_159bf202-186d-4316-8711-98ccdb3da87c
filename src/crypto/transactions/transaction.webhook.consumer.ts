import { Injectable } from '@nestjs/common';
import { RabbitmqService } from '@crednet/utils';
import { Events } from '../../utils/queue';
import { BaseWebhookConsumer } from '../../utils/webhook/base-webhook.consumer';
import { WebhookEventHandlerFactory } from '../../utils/webhook/webhook-event.handler';
import {
  WithdrawalSuccessEventHandler,
  WithdrawalFailedEventHandler,
} from './transactions.event-handlers';

@Injectable()
export class TransactionsWebhookConsumer extends BaseWebhookConsumer {
  constructor(
    protected readonly rmqService: RabbitmqService,
    private readonly eventHandlerFactory: WebhookEventHandlerFactory,
    private readonly withdrawalSuccessHandler: WithdrawalSuccessEventHandler,
    private readonly withdrawalFailedHandler: WithdrawalFailedEventHandler,
  ) {
    super(rmqService);
  }

  /**
   * Register event handlers for transaction events
   */
  protected registerEventHandlers(): void {
    // Register event handlers with the factory
    this.eventHandlerFactory.registerHandler(
      Events.WITHDRAWAL_SUCCESS,
      this.withdrawalSuccessHandler,
    );
    this.eventHandlerFactory.registerHandler(
      Events.WITHDRAWAL_FAILED,
      this.withdrawalFailedHandler,
    );

    // Subscribe to events
    this.subscribe(Events.WITHDRAWAL_SUCCESS);
    this.subscribe(Events.WITHDRAWAL_FAILED);
  }

  /**
   * Get the handler for an event
   * @param eventName The event name
   * @returns A function that handles the event
   */
  protected getEventHandler(eventName: string): (data: any) => Promise<void> {
    const handler = this.eventHandlerFactory.getHandler(eventName);
    if (handler) {
      return handler.handle.bind(handler);
    }
    return null;
  }
}
