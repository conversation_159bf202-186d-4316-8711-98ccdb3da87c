import { Injectable } from '@nestjs/common';
import { RabbitmqService } from '@crednet/utils';
import { PaymentEvents, Exchanges } from '../../utils/queue';
import { BaseWebhookConsumer } from '../../utils/webhook/base-webhook.consumer';
import { WebhookEventHandlerFactory } from '../../utils/webhook/webhook-event.handler';
import { PaymentProcessedEventHandler } from './transactions.event-handlers';

/**
 * Consumer for payment-related events
 */
@Injectable()
export class TransactionPaymentConsumer extends BaseWebhookConsumer {
  constructor(
    protected readonly rmqService: RabbitmqService,
    private readonly eventHandlerFactory: WebhookEventHandlerFactory,
    private readonly paymentProcessedHandler: PaymentProcessedEventHandler,
  ) {
    super(rmqService);
  }

  /**
   * Register event handlers for payment events
   */
  protected registerEventHandlers(): void {
    // Register event handlers with the factory
    this.eventHandlerFactory.registerHandler(
      PaymentEvents.FUND_PAYMENT_STATUS,
      this.paymentProcessedHandler,
    );

    // Subscribe to events - note we're using a different exchange here
    this.subscribeToPaymentEvent(PaymentEvents.FUND_PAYMENT_STATUS);
  }

  /**
   * Subscribe to a payment event
   * @param eventName The event to subscribe to
   */
  private subscribeToPaymentEvent(eventName: string): void {
    this.rmqService.subscribe(
      `${Exchanges.PAYMENT}.${eventName}`,
      this.handleWebhookEvent.bind(this),
    );
    this.logger.log(`Subscribed to payment event: ${eventName}`);
  }

  /**
   * Get the handler for an event
   * @param eventName The event name
   * @returns A function that handles the event
   */
  protected getEventHandler(eventName: string): (data: any) => Promise<void> {
    const handler = this.eventHandlerFactory.getHandler(eventName);
    if (handler) {
      return handler.handle.bind(handler);
    }
    return null;
  }
}
