import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { TransactionsService } from './transactions.service';
import { GetAuthData, JwtAuthGuard, AuthData } from '@crednet/authmanager';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import {
  CreateTransactionDto,
  FundTransactionDto,
  WithdrawalTransactionDto,
} from '../dtos/transaction.dto';

@Controller('transactions')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('transactions')
export class TransactionsController {
  constructor(private readonly transactionsService: TransactionsService) {}
  @Post('/fund')
  async fundCryptoWallet(
    @GetAuthData() auth: AuthData,
    @Body() createTransactionDto: FundTransactionDto,
  ) {
    return this.transactionsService.initfundCryptoWallet(
      createTransactionDto,
      auth,
    );
  }

  @Post('/transfer')
  async transferToExternalWallet(
    @GetAuthData() auth: AuthData,
    @Body() createTransactionDto: CreateTransactionDto,
  ) {
    return this.transactionsService.transferToExternalWallet(
      createTransactionDto,
      auth,
    );
  }

  @Post('/withdrawal')
  async withdrawIntoCashWallet(
    @GetAuthData() auth: AuthData,
    @Body() createTransactionDto: WithdrawalTransactionDto,
  ) {
    return this.transactionsService.initiateWithdrawal(
      createTransactionDto,
      auth,
    );
  }
}
