import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Logger } from '@nestjs/common';
import { Events } from 'src/utils/queue';
import { TransactionsService } from './transactions.service';

@Processor(Events.REQUERY_TRANSACTION)
export class VerifyTransactionConsumer extends WorkerHost {
  private readonly logger = new Logger(VerifyTransactionConsumer.name);

  constructor(private readonly transactionsService: TransactionsService) {
    super();
  }

  async process(job: Job) {
    try {
      this.logger.debug(
        `Processing requery job for ${Events.REQUERY_TRANSACTION}, `,
        job.asJSON(),
      );
      const data: { reference: string } = job.data;

      await this.transactionsService.requery(data.reference);
    } catch (error) {
      this.logger.error(
        `Error processing requery job: ${error.message}`,
        error.stack,
      );
      throw error; // Rethrow to trigger backoff retry
    }
  }
}
