import { Injectable, Logger } from '@nestjs/common';
import {
  PaymentTransaction,
  PaymentTransactionStatus,
  PaymentTransactionWalletType,
} from '@crednet/utils';
import { WebhookEventHandler } from '../../utils/webhook/webhook-event.handler';
import { TransactionsService } from './transactions.service';
import { TransactionRepository } from '../repositories/transaction.repository';
import {
  Transaction,
  TransactionStatus,
  TransactionType,
} from '../entities/transactions.entity';
import { WithdrawalSuccessPortfolioHandler } from '../portfolio/portfolio.event-handlers';

/**
 * Base handler for transaction events
 */
@Injectable()
export abstract class BaseTransactionEventHandler
  implements WebhookEventHandler
{
  protected readonly logger = new Logger(BaseTransactionEventHandler.name);

  constructor(
    protected readonly transactionsService: TransactionsService,
    protected readonly transactionRepository: TransactionRepository,
  ) {}

  abstract handle(data: any): Promise<void>;
}

/**
 * Handler for withdrawal success events
 */
@Injectable()
export class WithdrawalSuccessEventHandler extends BaseTransactionEventHandler {
  constructor(
    protected readonly transactionsService: TransactionsService,
    protected readonly transactionRepository: TransactionRepository,
    private readonly portfolioHandler: WithdrawalSuccessPortfolioHandler,
  ) {
    super(transactionsService, transactionRepository);
  }

  async handle(data: any): Promise<void> {
    const { status, reference } = data;

    if (status && status === 'Done') {
      // Update transaction status
      await this.transactionsService.handleSuccess(reference, data);

      try {
        await this.portfolioHandler.handle(data);
      } catch (error) {
        this.logger.error(
          'Error triggering portfolio snapshot for withdrawal success:',
          error,
        );
      }
    }
  }
}

/**
 * Handler for withdrawal failed events
 */
@Injectable()
export class WithdrawalFailedEventHandler extends BaseTransactionEventHandler {
  async handle(data: any): Promise<void> {
    const { status, reference } = data;

    if (status && status === 'Rejected') {
      // Update transaction status
      await this.transactionRepository.update(
        { reference },
        { status: TransactionStatus.FAILED },
      );

      // Get the transaction to log information
      const transaction = await this.transactionRepository.findOne({
        where: { reference },
        relations: ['user', 'wallet'],
      });

      if (transaction.type === TransactionType.FUND) {
        this.transactionsService.refundTransaction(reference);
      }

      if (transaction) {
        // TODO: Send notification to user when notification service is implemented
        this.logger.log(
          `Transaction failed for user ${transaction.user.userId}, reference: ${reference}`,
        );
      }
    }
  }
}

/**
 * Handler for payment processed events
 */
@Injectable()
export class PaymentProcessedEventHandler implements WebhookEventHandler {
  private readonly logger = new Logger(PaymentProcessedEventHandler.name);

  constructor(
    private readonly transactionRepository: TransactionRepository,
    private readonly transactionService: TransactionsService,
  ) {}

  async handle(data: any): Promise<void> {
    const { status, reference } = data;
    const transaction = await this.transactionRepository.findOne({
      where: { reference },
      relations: ['user', 'wallet'],
    });

    if (
      (data?.paymentStatus != TransactionStatus.SUCCESS &&
        data?.paymentStatus != TransactionStatus.FAILED) ||
      (status == PaymentTransactionStatus.REVERSED && !data?.isRefunded)
    ) {
      switch (status) {
        case PaymentTransactionStatus.SUCCESSFUL:
          this.handleSuccess(transaction);
          break;
        case PaymentTransactionStatus.FAILED:
          this.handleFailed(transaction, data.errors);
          break;
        case PaymentTransactionStatus.REVERSED:
          this.handleRefunded(transaction);
          break;

        case PaymentTransactionStatus.NOT_FOUND:
          if (
            transaction.paymentStatus == TransactionStatus.PENDING ||
            transaction.paymentStatus == TransactionStatus.PROCESSING
          ) {
            this.handleNotFound(transaction, data.transaction);
          }
          break;
        default:
          break;
      }
    }

    this.logger.log('Payment processed event handler', data);
  }
  private async handleSuccess(data: Transaction) {
    const update = { paymentStatus: TransactionStatus.SUCCESS };
    if (data.walletType) {
      update['walletType'] = data.walletType;
    }
    await this.transactionRepository.update({ id: data.id }, update);

    if (data.type == TransactionType.FUND) {
      await this.transactionService.finalisePayment(data);
    }

    if (data.type == TransactionType.WITHDRAWAL) {
      // todo: send notification to user
    }
  }

  private async handleFailed(data: Transaction, error: object) {
    const update = {
      paymentStatus: TransactionStatus.FAILED,
      status: TransactionStatus.FAILED,
      errors: error,
    };
    if (data.walletType) {
      update['walletType'] = data.walletType;
    }
    await this.transactionRepository.update({ id: data.id }, update);
  }

  private async handleRefunded(data: Transaction) {
    const update = { isRefunded: true };
    if (
      data.status == TransactionStatus.PENDING ||
      data.status == TransactionStatus.PROCESSING
    ) {
      update['status'] = TransactionStatus.FAILED;
    }

    if (
      data.paymentStatus == TransactionStatus.PROCESSING ||
      data.paymentStatus == TransactionStatus.PENDING
    ) {
      update['paymentStatus'] = TransactionStatus.FAILED;
    }
    await this.transactionRepository.update({ id: data.id }, update);
  }

  private async handleNotFound(
    data: Transaction,
    transaction: PaymentTransaction,
  ) {
    //TODO what is payment wallet response???
    const paymentWalletResponse = { ...data.meta?.paymentWalletResponse };
    if (!paymentWalletResponse[transaction.walletType]) {
      paymentWalletResponse[transaction.walletType] = transaction.status;
    }
    const meta = { ...data.meta, paymentWalletResponse };
    await this.transactionRepository.update({ id: data.id }, { meta });

    if (
      paymentWalletResponse[PaymentTransactionWalletType.CREDPAL_CREDIT] ==
        PaymentTransactionStatus.NOT_FOUND &&
      paymentWalletResponse[PaymentTransactionWalletType.CREDPAL_CASH] ==
        PaymentTransactionStatus.NOT_FOUND
    ) {
      const transactionAge = Date.now() - data.createdAt.getTime();
      const tenMinutesInMs = 10 * 60 * 1000; // 10 minutes in milliseconds

      if (transactionAge >= tenMinutesInMs) {
        await this.transactionRepository.update(
          { id: data.id },
          {
            meta,
            status: TransactionStatus.ABANDONED,
            paymentStatus: TransactionStatus.ABANDONED,
          },
        );
      }
    }
  }
}
