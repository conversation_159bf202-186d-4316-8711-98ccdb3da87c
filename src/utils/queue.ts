export enum Queues {
  CRYPTO = 'crypto_queue',
  // BILLS = 'bills_queue'
}

export enum Events {
  CREATE_WALLET = 'quidax.wallet.address.generated',
  WITHDRAWAL_SUCCESS = 'quidax.withdraw.successful',
  WITHDRAWAL_FAILED = 'quidax.withdraw.rejected',
  WALLET_UPDATED = 'quidax.wallet.updated',
  ORDER_DONE = 'quidax.order.done',
  ORDER_CANCELLED = 'quidax.order.cancelled',
  DEPOSIT_SUCCESSFUL = 'quidax.deposit.successful',
  DEPOSIT_FAILED_AML = 'quidax.deposit.failed_aml',
  DEPOSIT_ON_HOLD = 'quidax.deposit.on_hold',
  DEPOSIT_COMPLETED = 'quidax.deposit.transaction.confirmation',
  SWAP_TRANSACTION_COMPLETED = 'quidax.swap_transaction.completed',
  SWAP_TRANSACTION_FAILED = 'quidax.swap_transaction.failed',
  SWAP_TRANSACTION_REVERSED = 'quidax.swap_transaction.reversed',
  REQUERY_TRANSACTION = 'requery_transaction',
}

export enum Exchanges {
  WEBHOOK = 'webhook',
  PAYMENT = 'payment',
  // BILLS = 'bills_queue'
}

export enum PaymentRequestEventTypes {
  PAYMENT_REQUEST_FUND = 'cp.payment.charge',
  PAYMENT_REQUEST_FINALIZE = 'cp.payment.finalize',
  PAYMENT_RESPONSE_STATUS = 'cp.payment.status',
  REVERSE_TRANSACTION = 'reverse-transaction',
  QUERY_TRANSACTION = 'query-transaction',
}

export enum PaymentEvents {
  FUND_PAYMENT_STATUS = 'fund_payment_status',
  TOP_UP = 'top-up',
  CARD_PAYMENT_STATUS = 'card_payment_status',

  // PROCESS_PAYMENT = 'payment_processed',
  // PAYMENT_FAILED = 'payment_failed',
  // REQUERY_TRANSACTION = 'requery_transaction'
}
