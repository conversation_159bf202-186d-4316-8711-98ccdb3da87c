import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { RabbitmqService } from '@crednet/utils';
import { Exchanges } from '../queue';

/**
 * Base class for all webhook consumers
 * Provides common functionality for subscribing to events and handling errors
 */
@Injectable()
export abstract class BaseWebhookConsumer implements OnModuleInit {
  protected readonly logger = new Logger(this.constructor.name);

  constructor(protected readonly rmqService: RabbitmqService) {}

  /**
   * Subscribe to events when the module initializes
   */
  async onModuleInit(): Promise<void> {
    this.registerEventHandlers();
  }

  /**
   * Register event handlers for the events this consumer is interested in
   * This method should be implemented by subclasses
   */
  protected abstract registerEventHandlers(): void;

  /**
   * Subscribe to an event
   * @param eventName The event to subscribe to
   */
  protected subscribe(eventName: string): void {
    this.rmqService.subscribe(
      `${Exchanges.WEBHOOK}.${eventName}`,
      this.handleWebhookEvent.bind(this),
    );
    this.logger.log(`Subscribed to event: ${eventName}`);
  }

  /**
   * Handle a webhook event
   * This method is called when an event is received
   * @param param0 The event data
   */
  protected async handleWebhookEvent({
    message,
    data,
    ack,
    reject,
  }): Promise<void> {
    try {
      const eventName = message.fields.routingKey;

      this.logger.debug(`Handling webhook event: ${eventName}`);

      const handler = this.getEventHandler(eventName);

      if (handler) {
        await handler(data);
        this.logger.debug(`Successfully processed event: ${eventName}`);
        ack();
      } else {
        this.logger.warn(`No handler found for event: ${eventName}`);
        reject();
      }
    } catch (error) {
      this.logger.error(
        `Error handling webhook event: ${error.message}`,
        error.stack,
      );
      reject();
    }
  }

  /**
   * Get the handler for an event
   * This method should be implemented by subclasses
   * @param eventName The event name
   * @returns A function that handles the event
   */
  protected abstract getEventHandler(
    eventName: string,
  ): (data: any) => Promise<void>;
}
