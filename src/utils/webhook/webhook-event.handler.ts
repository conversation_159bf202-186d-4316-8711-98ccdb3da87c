import { Injectable, Logger } from '@nestjs/common';

/**
 * Interface for webhook event handlers
 */
export interface WebhookEventHandler {
  handle(data: any): Promise<void>;
}

/**
 * Factory for creating webhook event handlers
 */
@Injectable()
export class WebhookEventHandlerFactory {
  private readonly logger = new Logger(WebhookEventHandlerFactory.name);
  private readonly handlers = new Map<string, WebhookEventHandler>();

  /**
   * Register a handler for an event
   * @param eventName The event name
   * @param handler The handler for the event
   */
  registerHandler(eventName: string, handler: WebhookEventHandler): void {
    this.handlers.set(eventName, handler);
    this.logger.log(`Registered handler for event: ${eventName}`);
  }

  /**
   * Get a handler for an event
   * @param eventName The event name
   * @returns The handler for the event, or undefined if no handler is registered
   */
  getHandler(eventName: string): WebhookEventHandler | undefined {
    return this.handlers.get(eventName);
  }

  /**
   * Check if a handler is registered for an event
   * @param eventName The event name
   * @returns True if a handler is registered, false otherwise
   */
  hasHandler(eventName: string): boolean {
    return this.handlers.has(eventName);
  }
}
