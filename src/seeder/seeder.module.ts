import { Modu<PERSON> } from '@nestjs/common';
import { QuidaxModule } from '@app/quidax';
import { SeederService } from './seeder.service';
import { Network } from '../crypto/entities/network.entity';
import { Currency } from '../crypto/entities/currency.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InternalCacheService } from '@app/internal-cache';
import { SeedersCron } from './seeder.cron';

@Module({
  imports: [QuidaxModule, TypeOrmModule.forFeature([Currency, Network])],
  providers: [SeederService, SeedersCron, InternalCacheService],
})
export class SeederModule {}
