import { Injectable } from '@nestjs/common';
import { QuidaxService } from '../../libs/quidax/src';
import { DataSource, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Network } from '../crypto/entities/network.entity';
import { Currency } from '../crypto/entities/currency.entity';
import { InternalCacheService } from '@app/internal-cache'; // Adjust the import path as needed

@Injectable()
export class SeederService {
  constructor(
    private readonly quidaxService: QuidaxService,
    private readonly dataSource: DataSource,
    private readonly internalCacheService: InternalCacheService, // Inject the cache service
    @InjectRepository(Currency)
    private readonly currencyRepository: Repository<Currency>,
    @InjectRepository(Network)
    private readonly networkRepository: Repository<Network>,
  ) {}

  async seed() {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();

    try {
      const apiResponse = await this.quidaxService.fetchUserWallets('me');
      for (const data of apiResponse) {
        const currencyCacheKey = `crypto:currency_${data.currency}`;

        let currency =
          await this.internalCacheService.get<Currency>(currencyCacheKey);
        if (!currency) {
          currency = await this.currencyRepository.findOne({
            where: { currencyCode: data.currency },
          });
          if (!currency) {
            currency = this.currencyRepository.create({
              id: data.id,
              name: data.name,
              currencyCode: data.currency,
              isCrypto: data.is_crypto,
              referenceCurrency: data.reference_currency,
              blockchainEnabled: data.blockchain_enabled,
              defaultNetwork: data.default_network,
              networks: [], // Initialize networks as an empty array
            });
            await this.currencyRepository.save(currency);
          }

          for (const net of data.networks) {
            let network = await this.networkRepository.findOne({
              where: { id: net.id },
            });
            const networkData = {
              id: net.id,
              name: net.name,
              depositsEnabled: net.deposits_enabled,
              withdrawsEnabled: net.withdraws_enabled,
            };
            if (!network) {
              network = this.networkRepository.create(networkData);
              await this.networkRepository.save(network);
            }

            if (!currency.networks) {
              currency.networks = []; // Ensure networks is an array
            }
            currency.networks.push(network);
            await this.internalCacheService
              .tags('crypto', 'currencies')
              .set(currencyCacheKey, currency);

            const currencyWithNetworks = await this.currencyRepository.findOne({
              where: { id: currency.id },
              relations: ['networks'],
            });
            if (
              currencyWithNetworks &&
              !currencyWithNetworks.networks.some((n) => n.id === network.id)
            ) {
              currencyWithNetworks.networks.push(network);
              await this.currencyRepository.save(currencyWithNetworks);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error seeding data:', error);
    } finally {
      await queryRunner.release();
    }
  }
}
