# # Use Node.js as the base image
# FROM node:18-alpine

# # Set the working directory inside the container
# WORKDIR /app

# # Copy .npmrc file for authentication/configuration (if applicable)
# COPY .npmrc ./

# # Copy package.json and package-lock.json before copying the whole codebase
# COPY package*.json ./

# # Install dependencies
# RUN npm install --legacy-peer-deps

# # Copy the rest of the application code
# COPY . .

# # Ensure NestJS is installed inside the container
## RUN npm install -g @nestjs/cli

# # Build the application
# RUN npm run build

# # Expose the port
# EXPOSE 3000

# # Start the application
# CMD ["node", "dist/main"]


# Use Node.js 20 as the base image
FROM node:20-alpine AS builder

# Set the working directory inside the container
WORKDIR /app

# Copy .npmrc file for authentication/configuration (if applicable)
COPY .npmrc ./

# Copy package.json and yarn.lock before copying the whole codebase
COPY package.json yarn.lock ./

# Install dependencies
RUN yarn install --frozen-lockfile

# Copy the rest of the application code
COPY . .

# Build the application
RUN yarn build

# Use a smaller, final runtime image
FROM node:20-alpine AS runner
WORKDIR /app

# Copy .npmrc file (if needed in runtime)
COPY --from=builder /app/.npmrc ./

# Copy built files from the builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

# Expose the port
EXPOSE 3000

# Start the application
CMD ["node", "dist/src/main"]



